# 任务参数配置指南

## 概述

真实数据集评估框架现在支持灵活的任务参数配置，您可以选择执行单个任务、多个任务组合，或者全部任务。

## 支持的任务类型

1. **因果发现评估 (Causal Discovery)**
   - 有真实因果邻接矩阵的数据集
   - 支持节点关系分析和特征重要性评估
   - 输出：模型R²箱线图 + 特征重要性箱线图 + JSON详细结果 + CSV汇总

2. **因果效应评估 (Causal Inference)**
   - 使用meta-learner框架评估因果效应估计能力
   - 支持S-learner和T-learner方法
   - 输出：模型性能箱线图 + CSV汇总

3. **分布外泛化评估 (Out-of-Distribution)**
   - 评估模型在不同分布上的泛化能力
   - 支持多个目标域评估
   - 输出：模型泛化性能箱线图 + CSV汇总

## 使用方式

### 1. 交互式选择（推荐）

```bash
python real_data_evaluator.py
```

运行后会显示任务选择菜单：

```
可选择的评估任务:
1. 因果发现评估 (Causal Discovery)
2. 因果效应评估 (Causal Inference)
3. 分布外泛化评估 (Out-of-Distribution)

选择方式:
- 单选: 输入单个数字 (如: 1)
- 多选: 输入多个数字用逗号分隔 (如: 1,2 或 1,3 或 2,3)
- 全选: 输入 all 或 1,2,3

请输入选择 (默认: 1,2,3):
```

### 2. 程序化配置

```python
from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    create_sample_causal_discovery_configs,
    create_sample_causal_inference_configs,
    create_sample_ood_configs
)
from scm_test import ModelConfig

# 配置模型
model_config = ModelConfig(
    ols=True,
    xgboost=True,
    tabpfn_default=True
)

# 示例1: 只执行因果发现评估
config1 = RealDataEvaluationConfig(
    causal_discovery_datasets=create_sample_causal_discovery_configs(),
    causal_inference_datasets=None,
    ood_datasets=None,
    model_config=model_config,
    device='cuda',
    output_dir='results_discovery_only'
)

# 示例2: 执行因果发现 + 因果推断评估
config2 = RealDataEvaluationConfig(
    causal_discovery_datasets=create_sample_causal_discovery_configs(),
    causal_inference_datasets=create_sample_causal_inference_configs(),
    ood_datasets=None,
    model_config=model_config,
    device='cuda',
    output_dir='results_discovery_inference'
)

# 示例3: 执行所有任务
config3 = RealDataEvaluationConfig(
    causal_discovery_datasets=create_sample_causal_discovery_configs(),
    causal_inference_datasets=create_sample_causal_inference_configs(),
    ood_datasets=create_sample_ood_configs(),
    model_config=model_config,
    device='cuda',
    output_dir='results_complete'
)

# 执行评估
results = evaluate_real_datasets(config1)  # 选择要执行的配置
```

### 3. 使用配置示例

```bash
python task_config_examples.py
```

这个脚本提供了更多的配置示例和辅助函数。

## 任务选择示例

### 单选示例
- 输入 `1`：只执行因果发现评估
- 输入 `2`：只执行因果效应评估
- 输入 `3`：只执行分布外泛化评估

### 多选示例
- 输入 `1,2`：执行因果发现 + 因果效应评估
- 输入 `1,3`：执行因果发现 + 分布外泛化评估
- 输入 `2,3`：执行因果效应 + 分布外泛化评估

### 全选示例
- 输入 `1,2,3` 或 `all` 或直接回车：执行所有任务

## 输出目录结构

根据选择的任务组合，系统会自动生成相应的输出目录：

- 单选：`evaluation_results_discovery`、`evaluation_results_inference`、`evaluation_results_ood`
- 多选：`evaluation_results_discovery_inference`、`evaluation_results_discovery_ood`、`evaluation_results_inference_ood`
- 全选：`evaluation_results_discovery_inference_ood`

## 内置数据集

### 因果发现数据集（6个）
- Sachs：蛋白质信号网络
- Child：儿童疾病诊断
- Alarm：医疗监控网络
- Asia：肺癌诊断网络
- Cancer：癌症风险评估
- Earthquake：地震预测网络

### 因果推断数据集（6个）
- IHDP：婴儿健康发展项目
- Jobs：就业培训项目
- Twins：双胞胎研究
- ACIC：因果推断竞赛数据
- News：新闻推荐效果
- LaLonde：劳动经济学研究

### OOD数据集（6个）
- WILDS Camelyon：医疗图像跨医院
- DomainNet：跨域图像分类
- PACS：照片、艺术、卡通、素描
- Office-Home：办公用品跨域
- Terra Incognita：地理位置泛化
- VLCS：视觉概念跨域

## 高级配置

### 自定义数据集

您可以替换内置的数据集配置函数：

```python
# 自定义因果发现数据集
my_causal_datasets = [
    CausalDiscoveryDatasetConfig(
        name="my_dataset",
        data_path="data/my_data.csv",
        adj_matrix_path="data/my_adj.csv",
        target_node="target",
        feature_columns=["feat1", "feat2", "feat3"]
    )
]

config = RealDataEvaluationConfig(
    causal_discovery_datasets=my_causal_datasets,
    causal_inference_datasets=None,
    ood_datasets=None,
    model_config=model_config,
    device='cuda',
    output_dir='my_custom_results'
)
```

### 模型配置

```python
# 自定义模型配置
model_config = ModelConfig(
    ols=True,              # 普通最小二乘法
    lasso=False,           # Lasso回归
    catboost=True,         # CatBoost
    xgboost=True,          # XGBoost
    lightgbm=False,        # LightGBM
    tabpfn_default=True,   # TabPFN默认版本
    tabpfn_mse=True,       # TabPFN MSE版本
    tabpfn_muzero=True     # TabPFN Muzero版本
)
```

## 注意事项

1. **设备选择**：系统会自动检测CUDA可用性，建议使用GPU加速
2. **内存需求**：完整评估需要较大内存，可以选择部分任务执行
3. **执行时间**：不同任务的执行时间差异较大，因果发现评估通常最耗时
4. **结果保存**：每次运行会创建带时间戳的输出目录，避免结果覆盖

## 故障排除

1. **输入格式错误**：系统会自动处理格式错误，默认执行所有任务
2. **数据加载失败**：检查数据文件路径和格式
3. **模型训练失败**：检查GPU内存和数据质量
4. **结果保存失败**：检查输出目录权限

更多详细信息请参考代码注释和示例文件。
