# 真实数据集评估框架使用指南

## 概述

`real_data_evaluator.py` 提供了对三类真实数据集的统一评估功能：

1. **因果发现数据集**：有真实因果邻接矩阵，支持节点关系分析
2. **因果推断数据集**：使用meta-learner框架评估因果效应估计能力  
3. **分布外泛化数据集**：评估模型在不同分布上的泛化能力

## 快速开始

### 运行内置示例

```bash
python real_data_evaluator.py
```

运行后会提示选择模式：
- **模式1（简单示例）**：使用少量数据集进行快速测试
- **模式2（完整批量评估）**：使用所有内置示例数据集进行完整评估

### 自定义配置

```python
from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    CausalDiscoveryDatasetConfig,
    CausalInferenceDatasetConfig,
    OODDatasetConfig
)
from scm_test import ModelConfig

# 配置模型
model_config = ModelConfig(
    ols=True,
    xgboost=True,
    tabpfn_default=True
)

# 配置数据集
causal_datasets = [
    CausalDiscoveryDatasetConfig(
        name="my_dataset",
        data_path="data/my_data.csv",
        adj_matrix_path="data/my_adj.csv",
        target_node="target",
        feature_columns=["feat1", "feat2", "feat3"]
    )
]

# 创建评估配置
config = RealDataEvaluationConfig(
    causal_discovery_datasets=causal_datasets,
    model_config=model_config,
    device='cuda',
    output_dir='my_results'
)

# 执行评估
results = evaluate_real_datasets(config)
```

## 数据格式要求

### 因果发现数据集

**数据文件**（CSV格式）：
```csv
feat1,feat2,feat3,target
1.2,0.8,1.5,2.1
0.9,1.1,0.7,1.8
...
```

**邻接矩阵文件**（CSV格式，行列名为节点名）：
```csv
,feat1,feat2,feat3,target
feat1,0,1,0,0
feat2,0,0,1,0
feat3,0,0,0,1
target,0,0,0,0
```

### 因果推断数据集

```csv
feat1,feat2,feat3,treatment,outcome
0.1,0.2,0.3,1,2.3
0.2,0.3,0.4,0,1.8
...
```

### OOD数据集

**训练数据**：
```csv
feat1,feat2,feat3,target
1.2,0.8,1.5,0
0.9,1.1,0.7,1
...
```

**测试数据**（每个域一个文件）：
```csv
feat1,feat2,feat3,target
1.5,0.9,1.2,1
0.8,1.2,0.6,0
...
```

## 内置数据集示例

### 因果发现数据集（6个）
- **Sachs**：蛋白质信号网络
- **Child**：儿童疾病诊断
- **Alarm**：医疗监控网络
- **Asia**：肺癌诊断网络
- **Cancer**：癌症风险评估
- **Earthquake**：地震预测网络

### 因果推断数据集（6个）
- **IHDP**：婴儿健康发展项目
- **Jobs**：就业培训项目
- **Twins**：双胞胎研究
- **ACIC**：因果推断竞赛数据
- **News**：新闻推荐效果
- **LaLonde**：劳动经济学研究

### OOD数据集（6个）
- **WILDS Camelyon**：医疗图像跨医院
- **DomainNet**：跨域图像分类
- **PACS**：照片、艺术、卡通、素描
- **Office-Home**：办公用品跨域
- **Terra Incognita**：地理位置泛化
- **VLCS**：视觉概念跨域

## 配置选项

### ModelConfig
```python
ModelConfig(
    ols=True,              # 普通最小二乘法
    lasso=False,           # Lasso回归
    catboost=False,        # CatBoost
    xgboost=True,          # XGBoost
    lightgbm=False,        # LightGBM
    tabpfn_default=True,   # TabPFN默认版本
    tabpfn_mse=True,       # TabPFN MSE版本
    tabpfn_muzero=True     # TabPFN Muzero版本
)
```

### RealDataEvaluationConfig
```python
RealDataEvaluationConfig(
    causal_discovery_datasets=None,    # 因果发现数据集列表
    causal_inference_datasets=None,    # 因果推断数据集列表
    ood_datasets=None,                 # OOD数据集列表
    model_config=None,                 # 模型配置
    device='cuda',                     # 计算设备
    output_dir='real_data_results',    # 输出目录
    train_test_split_ratio=0.7         # 训练测试分割比例
)
```

## 输出结果

### 因果发现数据集
- `causal_discovery_model_comparison.png` - 模型R²对比箱线图
- `causal_discovery_importance_comparison.png` - 按节点类型的特征重要性箱线图
- `causal_discovery_results.json` - 详细结果（相关系数、互信息等）
- `causal_discovery_summary.csv` - 模型评估指标汇总

### 因果推断数据集
- `causal_inference_model_comparison.png` - 模型性能箱线图
- `causal_inference_summary.csv` - 因果效应估计结果汇总

### OOD数据集
- `ood_model_comparison.png` - 模型泛化性能箱线图
- `ood_summary.csv` - 跨域性能结果汇总

## 多数据集配置示例

```python
# 批量配置多个数据集
causal_inference_datasets = [
    CausalInferenceDatasetConfig(
        name="ihdp",
        data_path="data/ihdp/ihdp.csv",
        target_column="y_factual",
        feature_columns=["x1", "x2", "x3", "x4", "x5"],
        treatment_column="treatment"
    ),
    CausalInferenceDatasetConfig(
        name="jobs", 
        data_path="data/jobs/jobs.csv",
        target_column="re78",
        feature_columns=["age", "education", "black", "hispanic"],
        treatment_column="treat"
    ),
    # 更多数据集...
]

# OOD数据集配置
ood_datasets = [
    OODDatasetConfig(
        name="camelyon",
        train_data_path="data/camelyon/train.csv",
        test_data_paths={
            "hospital_1": "data/camelyon/test_hospital_1.csv",
            "hospital_2": "data/camelyon/test_hospital_2.csv",
            "hospital_3": "data/camelyon/test_hospital_3.csv"
        },
        target_column="y",
        feature_columns=["feat1", "feat2", "feat3"]
    ),
    # 更多数据集...
]
```

## 注意事项

1. **数据路径**：确保所有数据文件路径正确
2. **邻接矩阵**：支持CSV、NPY和TXT格式
3. **GPU支持**：自动检测CUDA可用性
4. **模型依赖**：确保所需的模型库已正确安装

## 故障排除

1. **导入错误**：检查依赖库安装
2. **数据加载失败**：验证数据文件格式和路径
3. **GPU内存不足**：使用CPU模式或减少数据量
4. **模型训练失败**：检查数据质量和模型配置

更多详细信息请参考代码注释。
