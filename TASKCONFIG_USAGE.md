# TaskConfig 使用指南

## 概述

真实数据集评估框架使用简化的 `TaskConfig` 类来配置任务，完全去掉了交互式输入，直接在代码中配置，就像配置 `ModelConfig` 一样简洁直观。

## TaskConfig 类

```python
@dataclass
class TaskConfig:
    """任务配置类 - 简化的任务选择"""
    causal_discovery: bool = True    # 因果发现评估
    causal_estimation: bool = True   # 因果效应评估  
    ood: bool = True                 # 分布外泛化评估
```

## 基本使用方式

### 1. 执行所有任务（默认）

```python
from real_data_evaluator import evaluate_real_datasets, RealDataEvaluationConfig, TaskConfig
from scm_test import ModelConfig

# 配置模型
model_config = ModelConfig(
    ols=True,
    xgboost=True,
    tabpfn_default=True
)

# 配置任务 - 执行所有任务
task_config = TaskConfig(
    causal_discovery=True,
    causal_estimation=True,
    ood=True
)

# 创建评估配置
config = RealDataEvaluationConfig(
    task_config=task_config,
    model_config=model_config,
    device='cuda',
    output_dir='results_all_tasks'
)

# 执行评估
results = evaluate_real_datasets(config)
```

### 2. 只执行单个任务

```python
# 只执行因果发现评估
task_config = TaskConfig(
    causal_discovery=True,
    causal_estimation=False,
    ood=False
)

# 只执行因果效应评估
task_config = TaskConfig(
    causal_discovery=False,
    causal_estimation=True,
    ood=False
)

# 只执行分布外泛化评估
task_config = TaskConfig(
    causal_discovery=False,
    causal_estimation=False,
    ood=True
)
```

### 3. 执行任务组合

```python
# 因果发现 + 因果效应评估
task_config = TaskConfig(
    causal_discovery=True,
    causal_estimation=True,
    ood=False
)

# 因果发现 + 分布外泛化评估
task_config = TaskConfig(
    causal_discovery=True,
    causal_estimation=False,
    ood=True
)

# 因果效应 + 分布外泛化评估
task_config = TaskConfig(
    causal_discovery=False,
    causal_estimation=True,
    ood=True
)
```

## 完整配置示例

```python
import torch
from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    TaskConfig
)
from scm_test import ModelConfig

def run_custom_evaluation():
    # 1. 配置模型
    model_config = ModelConfig(
        ols=True,
        lasso=True,
        catboost=True,
        xgboost=True,
        lightgbm=True,
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )
    
    # 2. 配置任务
    task_config = TaskConfig(
        causal_discovery=True,   # 执行因果发现评估
        causal_estimation=True,  # 执行因果效应评估
        ood=False               # 不执行分布外泛化评估
    )
    
    # 3. 创建评估配置
    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='my_evaluation_results'
    )
    
    # 4. 执行评估
    results = evaluate_real_datasets(config)
    
    return results

# 运行评估
results = run_custom_evaluation()
```

## 交互式使用

直接运行主程序仍然支持交互式选择：

```bash
python real_data_evaluator.py
```

会显示简化的菜单：

```
任务配置示例:
1. 全部任务
2. 只执行因果发现评估
3. 只执行因果效应评估
4. 只执行分布外泛化评估
5. 因果发现 + 因果效应评估
6. 自定义配置

请选择配置 (1-6, 默认: 1):
```

## 示例文件

运行示例文件查看更多用法：

```bash
python simple_task_examples.py
```

包含6个不同的配置示例：
1. 执行所有任务
2. 只执行因果发现评估
3. 只执行因果效应评估
4. 只执行分布外泛化评估
5. 因果发现 + 因果效应评估
6. 自定义配置

## 自动数据集配置

如果不提供自定义数据集，系统会自动使用内置示例数据集：

- **因果发现数据集**：Sachs, Child, Alarm, Asia, Cancer, Earthquake (6个)
- **因果推断数据集**：IHDP, Jobs, Twins, ACIC, News, LaLonde (6个)
- **OOD数据集**：WILDS Camelyon, DomainNet, PACS, Office-Home, Terra Incognita, VLCS (6个)

## 输出目录

系统会根据任务配置自动生成输出目录名：

- `discovery_20241230_143022`（只有因果发现）
- `estimation_20241230_143022`（只有因果效应）
- `ood_20241230_143022`（只有OOD）
- `discovery_estimation_20241230_143022`（因果发现+效应）
- `discovery_estimation_ood_20241230_143022`（全部任务）

## 优势对比

### 之前的方式（复杂）
```python
# 需要交互式输入
task_input = input("请输入选择 (默认: 1,2,3): ").strip()
# 需要解析输入
if not task_input or task_input.lower() == 'all':
    selected_tasks = [1, 2, 3]
# 需要根据数字配置数据集
if 1 in selected_tasks:
    causal_discovery_datasets = create_sample_causal_discovery_configs()
```

### 现在的方式（简洁）
```python
# 直接配置
task_config = TaskConfig(
    causal_discovery=True,
    causal_estimation=True,
    ood=True
)
```

## 注意事项

1. **向后兼容**：如果不提供 `task_config`，系统会根据数据集配置自动推断
2. **自动数据集**：启用任务但未提供数据集时，会自动使用内置示例
3. **设备选择**：建议使用GPU加速，系统会自动检测CUDA可用性
4. **输出目录**：每次运行会创建带时间戳的目录，避免结果覆盖

## 故障排除

1. **导入错误**：确保正确导入 `TaskConfig` 类
2. **配置错误**：检查布尔值设置是否正确
3. **设备问题**：如果GPU内存不足，设置 `device='cpu'`
4. **路径问题**：确保输出目录有写入权限

这种新的配置方式更加简洁直观，避免了复杂的交互式输入和解析逻辑！
