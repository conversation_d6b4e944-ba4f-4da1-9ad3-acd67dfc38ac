import rpy2.robjects as ro
from rpy2.robjects import pandas2ri
from rpy2.robjects.packages import importr

pandas2ri.activate()
pcalg = importr('pcalg')
base = importr('base')

# 加载 Sachs 数据集
ro.r('data(sachs)')
sachs_data = ro.r('sachs')
sachs_df = pandas2ri.rpy2py(sachs_data)
print("Sachs 数据集:")
print(sachs_df.head())

# 加载 Child 数据集
ro.r('data(child)')
child_data = ro.r('child')
child_df = pandas2ri.rpy2py(child_data)
print("\nChild 数据集:")
print(child_df.head())

# 获取 DAG 结构（邻接矩阵）
sachs_adj = ro.r('amat(sachs)')
sachs_adj_df = pandas2ri.rpy2py(sachs_adj)
print("\nSachs DAG 邻接矩阵:")
print(sachs_adj_df)

child_adj = ro.r('amat(child)')
child_adj_df = pandas2ri.rpy2py(child_adj)
print("\nChild DAG 邻接矩阵:")
print(child_adj_df)