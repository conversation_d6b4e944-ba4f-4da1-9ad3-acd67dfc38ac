# 从 URL 加载 Child 数据集
url = "http://www.bnlearn.com/bnrepository/discrete/child.csv"
child_data = pd.read_csv(url)
print(child_data.head())

# Child 数据集的已知 DAG 结构（邻接矩阵）
child_edges = [
    ('BirthAsphyxia', 'Disease'),
    ('Disease', 'LVH'),
    ('Disease', 'CardiacMixing'),
    ('CardiacMixing', 'HypoxiaInO2'),
    ('HypoxiaInO2', 'CO2Report'),
    ('HypoxiaInO2', 'CO2'),
    ('HypoxiaInO2', 'O2Saturation'),
    ('LungParench', 'LungFlow'),
    ('LungFlow', 'LungParench'),
    ('LungFlow', 'CO2'),
    ('LungFlow', 'O2Saturation'),
    ('LungParench', 'CO2'),
    ('LungParench', 'O2Saturation'),
    ('Age', 'LVH'),
    ('Age', 'CardiacMixing'),
    ('Age', 'Grunting'),
    ('Age', 'HypDistrib'),
    ('Age', 'HypoxiaInO2'),
    ('Age', 'CO2'),
    ('Age', 'O2Saturation'),
    ('Age', 'ChestXray'),
    ('Age', 'GruntingReport'),
    ('Age', 'LowerBodyO2'),
    ('Age', 'RUQO2'),
    ('Age', 'CO2Report'),
    ('Age', 'Disease'),
    ('Age', 'LungFlow'),
    ('Age', 'LungParench'),
    ('Age', 'Sick'),
    ('Age', 'DuctFlow'),
    ('Age', 'PulmAtresia'),
    ('Age', 'Ventilation'),
    ('Age', 'VentReport'),
    ('Age', 'VentMachine'),
    ('Age', 'MinVol'),
    ('Age', 'MinVolSet'),
    ('Age', 'Intubation'),
    ('Age', 'IntubationReport'),
    ('Age', 'VentilationReport'),
    ('Age', 'VentilationMachine'),
    ('Age', 'VentilationTube'),
    ('Age', 'VentilationEtt'),
    ('Age', 'VentilationMask'),
    ('Age', 'VentilationTrach'),
    ('Age', 'VentilationUmb'),
    ('Age', 'VentilationOther'),
    ('Age', 'VentilationNone'),
    ('Age', 'VentilationReport'),
    ('Age', 'VentilationMachine'),
    ('Age', 'VentilationTube'),
    ('Age', 'VentilationMask'),
    ('Age', 'VentilationTrach'),
    ('Age', 'VentilationUmb'),
    ('Age', 'VentilationOther'),
    ('Age', 'VentilationNone')
]

# 创建邻接矩阵
nodes = child_data.columns.tolist()
child_adj = pd.DataFrame(0, index=nodes, columns=nodes)

for source, target in child_edges:
    child_adj.loc[source, target] = 1

print("\nChild DAG 邻接矩阵:")
print(child_adj)