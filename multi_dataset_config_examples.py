"""
multi_dataset_config_examples.py - 多数据集配置示例

这个文件展示了如何配置多个真实数据集进行批量评估。
包含三类数据集的详细配置示例：
1. 因果发现数据集（多个有邻接矩阵的数据集）
2. 因果推断数据集（多个有treatment的数据集）
3. 分布外泛化数据集（多个跨域的数据集）
"""

from real_data_evaluator import (
    CausalDiscoveryDatasetConfig,
    CausalInferenceDatasetConfig,
    OODDatasetConfig,
    RealDataEvaluationConfig,
    evaluate_real_datasets
)
from scm_test import ModelConfig
import torch


def create_causal_discovery_configs():
    """创建多个因果发现数据集配置"""
    
    configs = [
        # Sachs数据集 - 蛋白质信号网络
        CausalDiscoveryDatasetConfig(
            name="sachs",
            data_path="data/sachs/sachs_data.csv",
            adj_matrix_path="data/sachs/sachs_adj.csv",
            target_node="Akt",
            feature_columns=["PKC", "Mek", "Raf", "Jnk", "P38", "PIP2", "PIP3", "Plcg"]
        ),
        
        # Child数据集 - 儿童疾病诊断
        CausalDiscoveryDatasetConfig(
            name="child",
            data_path="data/child/child_data.csv",
            adj_matrix_path="data/child/child_adj.csv",
            target_node="Disease",
            feature_columns=["Age", "Sex", "LungParench", "CO2", "ChestXray", "Grunting", "LVHreport", "LowerBodyO2", "RUQO2", "CO2Report", "XrayReport", "GruntingReport"]
        ),
        
        # Alarm数据集 - 医疗监控网络
        CausalDiscoveryDatasetConfig(
            name="alarm",
            data_path="data/alarm/alarm_data.csv",
            adj_matrix_path="data/alarm/alarm_adj.csv",
            target_node="HYPOVOLEMIA",
            feature_columns=["CVP", "PCWP", "HISTORY", "TPR", "BP", "CO", "HRBP", "HREK", "HRSAT", "ANAPHYLAXIS", "INSUFFANESTH", "PULMEMBOLUS", "KINKEDTUBE", "DISCONNECT", "MINVOLSET", "VENTMACH", "VENTTUBE", "VENTLUNG", "VENTALV", "ARTCO2", "CATECHOL", "HR", "ERRLOWOUTPUT", "HREKG", "ERRCAUTER", "HRSAT", "EXPCO2", "MINVOL", "FIO2", "PVSAT", "SAO2", "SHUNT"]
        ),
        
        # Asia数据集 - 肺癌诊断网络
        CausalDiscoveryDatasetConfig(
            name="asia",
            data_path="data/asia/asia_data.csv",
            adj_matrix_path="data/asia/asia_adj.csv",
            target_node="lung",
            feature_columns=["asia", "tub", "smoke", "bronc", "either", "xray", "dysp"]
        ),
        
        # Cancer数据集 - 癌症风险评估
        CausalDiscoveryDatasetConfig(
            name="cancer",
            data_path="data/cancer/cancer_data.csv",
            adj_matrix_path="data/cancer/cancer_adj.csv",
            target_node="Cancer",
            feature_columns=["Pollution", "Smoker", "Xray", "Dyspnoea"]
        ),
        
        # Earthquake数据集 - 地震预测网络
        CausalDiscoveryDatasetConfig(
            name="earthquake",
            data_path="data/earthquake/earthquake_data.csv",
            adj_matrix_path="data/earthquake/earthquake_adj.csv",
            target_node="Earthquake",
            feature_columns=["Burglary", "Alarm", "JohnCalls", "MaryCalls"]
        )
    ]
    
    return configs


def create_causal_inference_configs():
    """创建多个因果推断数据集配置"""
    
    configs = [
        # IHDP数据集 - 婴儿健康发展项目
        CausalInferenceDatasetConfig(
            name="ihdp",
            data_path="data/ihdp/ihdp.csv",
            target_column="y_factual",
            feature_columns=["x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10", "x11", "x12", "x13", "x14", "x15", "x16", "x17", "x18", "x19", "x20", "x21", "x22", "x23", "x24", "x25"],
            treatment_column="treatment"
        ),
        
        # Jobs数据集 - 就业培训项目
        CausalInferenceDatasetConfig(
            name="jobs",
            data_path="data/jobs/jobs.csv",
            target_column="re78",
            feature_columns=["age", "education", "black", "hispanic", "married", "nodegree", "re74", "re75"],
            treatment_column="treat"
        ),
        
        # Twins数据集 - 双胞胎研究
        CausalInferenceDatasetConfig(
            name="twins",
            data_path="data/twins/twins.csv",
            target_column="y",
            feature_columns=["gestat10", "birwt", "precare", "momage", "nprevistg", "dfageq", "feduc6", "infant", "omaps", "fmaps"],
            treatment_column="t"
        ),
        
        # ACIC 2016数据集 - 因果推断竞赛数据
        CausalInferenceDatasetConfig(
            name="acic_2016",
            data_path="data/acic/acic_2016.csv",
            target_column="Y",
            feature_columns=["X1", "X2", "X3", "X4", "X5", "X6", "X7", "X8", "X9", "X10", "X11", "X12", "X13", "X14", "X15", "X16", "X17", "X18", "X19", "X20"],
            treatment_column="Z"
        ),
        
        # News数据集 - 新闻推荐效果
        CausalInferenceDatasetConfig(
            name="news",
            data_path="data/news/news.csv",
            target_column="outcome",
            feature_columns=["n_tokens_title", "n_tokens_content", "n_unique_tokens", "n_non_stop_words", "num_hrefs", "num_self_hrefs", "num_imgs", "num_videos", "average_token_length", "num_keywords", "kw_min_min", "kw_max_min", "kw_avg_min", "kw_min_max", "kw_max_max", "kw_avg_max", "kw_min_avg", "kw_max_avg", "kw_avg_avg"],
            treatment_column="treatment"
        ),
        
        # LaLonde数据集 - 劳动经济学研究
        CausalInferenceDatasetConfig(
            name="lalonde",
            data_path="data/lalonde/lalonde.csv",
            target_column="re78",
            feature_columns=["age", "educ", "black", "hisp", "married", "nodegr", "re74", "re75", "u74", "u75"],
            treatment_column="treat"
        )
    ]
    
    return configs


def create_ood_configs():
    """创建多个OOD数据集配置"""
    
    configs = [
        # WILDS Camelyon数据集 - 医疗图像跨医院
        OODDatasetConfig(
            name="wilds_camelyon",
            train_data_path="data/camelyon/train.csv",
            test_data_paths={
                "hospital_1": "data/camelyon/test_hospital_1.csv",
                "hospital_2": "data/camelyon/test_hospital_2.csv",
                "hospital_3": "data/camelyon/test_hospital_3.csv",
                "hospital_4": "data/camelyon/test_hospital_4.csv",
                "hospital_5": "data/camelyon/test_hospital_5.csv"
            },
            target_column="y",
            feature_columns=["feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7", "feature_8", "feature_9", "feature_10"]
        ),
        
        # DomainNet数据集 - 跨域图像分类
        OODDatasetConfig(
            name="domainnet",
            train_data_path="data/domainnet/real_train.csv",
            test_data_paths={
                "clipart": "data/domainnet/clipart_test.csv",
                "painting": "data/domainnet/painting_test.csv",
                "sketch": "data/domainnet/sketch_test.csv",
                "infograph": "data/domainnet/infograph_test.csv",
                "quickdraw": "data/domainnet/quickdraw_test.csv"
            },
            target_column="label",
            feature_columns=["feat_1", "feat_2", "feat_3", "feat_4", "feat_5", "feat_6", "feat_7", "feat_8", "feat_9", "feat_10", "feat_11", "feat_12"]
        ),
        
        # PACS数据集 - 照片、艺术、卡通、素描
        OODDatasetConfig(
            name="pacs",
            train_data_path="data/pacs/photo_train.csv",
            test_data_paths={
                "art_painting": "data/pacs/art_painting_test.csv",
                "cartoon": "data/pacs/cartoon_test.csv",
                "sketch": "data/pacs/sketch_test.csv"
            },
            target_column="label",
            feature_columns=["pixel_1", "pixel_2", "pixel_3", "pixel_4", "pixel_5", "pixel_6", "pixel_7", "pixel_8", "pixel_9", "pixel_10", "pixel_11", "pixel_12", "pixel_13", "pixel_14", "pixel_15", "pixel_16"]
        ),
        
        # Office-Home数据集 - 办公用品跨域
        OODDatasetConfig(
            name="office_home",
            train_data_path="data/office_home/real_world_train.csv",
            test_data_paths={
                "art": "data/office_home/art_test.csv",
                "clipart": "data/office_home/clipart_test.csv",
                "product": "data/office_home/product_test.csv"
            },
            target_column="category",
            feature_columns=["feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7", "feature_8", "feature_9", "feature_10"]
        ),
        
        # Terra Incognita数据集 - 地理位置泛化
        OODDatasetConfig(
            name="terra_incognita",
            train_data_path="data/terra/location_100_train.csv",
            test_data_paths={
                "location_38": "data/terra/location_38_test.csv",
                "location_43": "data/terra/location_43_test.csv",
                "location_46": "data/terra/location_46_test.csv",
                "location_47": "data/terra/location_47_test.csv"
            },
            target_column="species",
            feature_columns=["env_1", "env_2", "env_3", "env_4", "env_5", "env_6", "env_7", "env_8", "env_9", "env_10", "env_11", "env_12", "env_13", "env_14", "env_15"]
        ),
        
        # VLCS数据集 - 视觉概念跨域
        OODDatasetConfig(
            name="vlcs",
            train_data_path="data/vlcs/caltech_train.csv",
            test_data_paths={
                "labelme": "data/vlcs/labelme_test.csv",
                "pascal": "data/vlcs/pascal_test.csv",
                "sun": "data/vlcs/sun_test.csv"
            },
            target_column="class",
            feature_columns=["visual_1", "visual_2", "visual_3", "visual_4", "visual_5", "visual_6", "visual_7", "visual_8", "visual_9", "visual_10", "visual_11", "visual_12", "visual_13", "visual_14", "visual_15", "visual_16"]
        )
    ]
    
    return configs


def run_batch_evaluation():
    """运行批量数据集评估"""
    
    print("创建多数据集配置...")
    
    # 创建所有数据集配置
    causal_discovery_configs = create_causal_discovery_configs()
    causal_inference_configs = create_causal_inference_configs()
    ood_configs = create_ood_configs()
    
    print(f"因果发现数据集: {len(causal_discovery_configs)} 个")
    print(f"因果推断数据集: {len(causal_inference_configs)} 个")
    print(f"OOD数据集: {len(ood_configs)} 个")
    
    # 配置模型
    model_config = ModelConfig(
        ols=True,
        lasso=True,
        catboost=False,  # 如果没有安装可以设为False
        xgboost=True,
        lightgbm=False,  # 如果没有安装可以设为False
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )
    
    # 创建评估配置
    config = RealDataEvaluationConfig(
        causal_discovery_datasets=causal_discovery_configs,
        causal_inference_datasets=causal_inference_configs,
        ood_datasets=ood_configs,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='batch_evaluation_results'
    )
    
    print(f"使用设备: {config.device}")
    print("开始批量评估...")
    
    # 执行评估
    results = evaluate_real_datasets(config)
    
    # 输出结果统计
    print("\n" + "="*60)
    print("批量评估结果统计")
    print("="*60)
    
    total_datasets = 0
    for dataset_type, type_results in results.items():
        print(f"\n{dataset_type.upper()}:")
        print(f"  成功评估: {len(type_results)} 个数据集")
        total_datasets += len(type_results)
        
        for result in type_results:
            dataset_name = result['dataset_name']
            if dataset_type == 'causal_discovery':
                n_models = len(result['model_results'])
                print(f"    {dataset_name}: {n_models} 个模型")
            elif dataset_type == 'causal_inference':
                n_methods = len(result['model_results'])
                print(f"    {dataset_name}: {n_methods} 个meta-learner方法")
            elif dataset_type == 'ood':
                n_domains = len(result['domain_results'])
                print(f"    {dataset_name}: {n_domains} 个目标域")
    
    print(f"\n总计成功评估: {total_datasets} 个数据集")
    print("所有结果已保存到 batch_evaluation_results 目录")
    
    return results


if __name__ == "__main__":
    # 运行批量评估
    results = run_batch_evaluation()
    
    print("\n批量评估完成！")
    print("可以查看生成的可视化图表和CSV文件了解详细结果。")
