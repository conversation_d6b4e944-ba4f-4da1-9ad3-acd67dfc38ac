# 真实数据集评估框架

这个框架提供了对三类真实数据集的统一评估功能，复用了现有的模型训练和评估组件，支持差异化的分析和可视化。

## 支持的数据集类型

### 1. 因果发现数据集（Causal Discovery Datasets）
- **特点**：有真实的因果邻接矩阵，可以获取节点关系
- **代表数据集**：Sachs, Child, Alarm, Asia等
- **分析内容**：
  - 完整的模型评估（R²、RMSE、MAPE）
  - 基于节点关系的特征重要性分析
  - 相关系数和互信息计算
  - 详细的JSON结果保存

### 2. 因果效应评估数据集（Causal Inference Datasets）
- **特点**：有treatment和outcome，用于评估因果效应估计能力
- **代表数据集**：IHDP, Jobs, Twins等
- **分析内容**：
  - S-learner和T-learner框架评估
  - 平均因果效应（ATE）估计
  - 基础模型性能评估

### 3. 分布外泛化数据集（Out-of-Distribution Datasets）
- **特点**：训练集和测试集来自不同分布，评估模型泛化能力
- **代表数据集**：WILDS, DomainNet, PACS等
- **分析内容**：
  - 源域和目标域性能对比
  - Domain gap分析
  - 跨域泛化能力评估

## 文件结构

```
real_data_evaluator.py      # 主要的评估框架
real_data_example.py        # 使用示例
README_real_data.md         # 说明文档（本文件）
```

## 快速开始

### 1. 基本使用

```python
from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    CausalDiscoveryDatasetConfig
)
from scm_test import ModelConfig

# 配置模型
model_config = ModelConfig(
    ols=True,
    xgboost=True,
    tabpfn_default=True
)

# 配置因果发现数据集
causal_datasets = [
    CausalDiscoveryDatasetConfig(
        name="sachs",
        data_path="data/sachs_data.csv",
        adj_matrix_path="data/sachs_adj.csv",
        target_node="Akt",
        feature_columns=["PKC", "Mek", "Raf", "Jnk"]
    )
]

# 创建评估配置
config = RealDataEvaluationConfig(
    causal_discovery_datasets=causal_datasets,
    model_config=model_config,
    device='cuda',
    output_dir='results'
)

# 执行评估
results = evaluate_real_datasets(config)
```

### 2. 运行示例

```bash
python real_data_example.py
```

## 数据格式要求

### 因果发现数据集

**数据文件格式**（CSV）：
```csv
PKC,Mek,Raf,Jnk,P38,PIP2,PIP3,Plcg,Akt
1.2,0.8,1.5,0.3,0.9,2.1,1.7,0.6,1.4
0.9,1.1,0.7,0.8,1.2,1.8,2.0,0.9,1.1
...
```

**邻接矩阵文件格式**（CSV）：
```csv
,PKC,Mek,Raf,Jnk,P38,PIP2,PIP3,Plcg,Akt
PKC,0,1,0,0,0,0,0,0,0
Mek,0,0,1,0,0,0,0,0,0
Raf,0,0,0,0,0,0,0,0,1
...
```

### 因果推断数据集

**数据文件格式**（CSV）：
```csv
x1,x2,x3,x4,x5,treatment,y_factual
0.1,0.2,0.3,0.4,0.5,1,2.3
0.2,0.3,0.4,0.5,0.6,0,1.8
...
```

### OOD数据集

**训练数据格式**（CSV）：
```csv
feature_1,feature_2,feature_3,y
1.2,0.8,1.5,0
0.9,1.1,0.7,1
...
```

**测试数据格式**（每个域一个文件）：
```csv
feature_1,feature_2,feature_3,y
1.5,0.9,1.2,1
0.8,1.2,0.6,0
...
```

## 输出结果

### 因果发现数据集输出
- `causal_discovery_model_comparison.png` - 模型R²对比箱线图
- `causal_discovery_importance_comparison.png` - 按节点类型的特征重要性箱线图
- `causal_discovery_results.json` - 详细结果（包含相关系数、互信息等）
- `causal_discovery_summary.csv` - 模型评估指标汇总

### 因果推断数据集输出
- `causal_inference_model_comparison.png` - 模型性能箱线图
- `causal_inference_summary.csv` - 因果效应估计结果汇总

### OOD数据集输出
- `ood_model_comparison.png` - 模型泛化性能箱线图
- `ood_summary.csv` - 跨域性能结果汇总

## 配置选项

### ModelConfig
```python
ModelConfig(
    ols=True,           # 普通最小二乘法
    lasso=False,        # Lasso回归
    catboost=False,     # CatBoost
    xgboost=True,       # XGBoost
    lightgbm=False,     # LightGBM
    tabpfn_default=True,    # TabPFN默认版本
    tabpfn_mse=True,        # TabPFN MSE版本
    tabpfn_muzero=True      # TabPFN Muzero版本
)
```

### RealDataEvaluationConfig
```python
RealDataEvaluationConfig(
    causal_discovery_datasets=None,    # 因果发现数据集列表
    causal_inference_datasets=None,    # 因果推断数据集列表
    ood_datasets=None,                 # OOD数据集列表
    model_config=None,                 # 模型配置
    device='cuda',                     # 计算设备
    output_dir='real_data_results',    # 输出目录
    train_test_split_ratio=0.7         # 训练测试分割比例
)
```

## 注意事项

1. **数据路径**：确保所有数据文件路径正确，支持相对路径和绝对路径
2. **邻接矩阵**：支持CSV、NPY和TXT格式，矩阵大小应与节点数量匹配
3. **GPU支持**：自动检测CUDA可用性，建议使用GPU加速
4. **内存管理**：大数据集可能需要较大内存，建议监控内存使用
5. **模型依赖**：确保所需的模型库已正确安装（TabPFN、CatBoost等）

## 扩展功能

框架设计支持轻松扩展：
- 添加新的数据集类型
- 集成新的机器学习模型
- 自定义评估指标
- 扩展可视化功能

## 故障排除

1. **导入错误**：检查是否正确安装了所有依赖库
2. **数据加载失败**：验证数据文件格式和路径
3. **GPU内存不足**：减少batch size或使用CPU模式
4. **模型训练失败**：检查数据质量和模型配置

更多详细信息请参考代码注释和示例文件。
