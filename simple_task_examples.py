"""
simple_task_examples.py - 简化的TaskConfig使用示例

展示如何使用新的TaskConfig类来配置真实数据集评估任务。
"""

from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    TaskConfig
)
from scm_test import ModelConfig
import torch


def example_1_all_tasks():
    """示例1: 执行所有任务"""
    
    print("示例1: 执行所有任务")
    print("="*40)
    
    # 配置模型
    model_config = ModelConfig(
        ols=True,
        xgboost=True,
        tabpfn_default=True
    )
    
    # 配置任务 - 执行所有任务
    task_config = TaskConfig(
        causal_discovery=True,
        causal_estimation=True,
        ood=True
    )
    
    # 创建评估配置
    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_all_tasks'
    )
    
    print("配置完成: 执行所有任务")
    print(f"  - 因果发现评估: {task_config.causal_discovery}")
    print(f"  - 因果效应评估: {task_config.causal_estimation}")
    print(f"  - 分布外泛化评估: {task_config.ood}")
    
    # 执行评估
    results = evaluate_real_datasets(config)
    return results


def example_2_causal_discovery_only():
    """示例2: 只执行因果发现评估"""
    
    print("示例2: 只执行因果发现评估")
    print("="*40)
    
    model_config = ModelConfig(
        ols=True,
        xgboost=True,
        tabpfn_default=True
    )
    
    # 只执行因果发现评估
    task_config = TaskConfig(
        causal_discovery=True,
        causal_estimation=False,
        ood=False
    )
    
    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_discovery_only'
    )
    
    print("配置完成: 只执行因果发现评估")
    
    results = evaluate_real_datasets(config)
    return results


def example_3_causal_estimation_only():
    """示例3: 只执行因果效应评估"""
    
    print("示例3: 只执行因果效应评估")
    print("="*40)
    
    model_config = ModelConfig(
        ols=True,
        xgboost=True,
        tabpfn_default=True
    )
    
    # 只执行因果效应评估
    task_config = TaskConfig(
        causal_discovery=False,
        causal_estimation=True,
        ood=False
    )
    
    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_estimation_only'
    )
    
    print("配置完成: 只执行因果效应评估")
    
    results = evaluate_real_datasets(config)
    return results


def example_4_ood_only():
    """示例4: 只执行分布外泛化评估"""
    
    print("示例4: 只执行分布外泛化评估")
    print("="*40)
    
    model_config = ModelConfig(
        ols=True,
        xgboost=True,
        tabpfn_default=True
    )
    
    # 只执行分布外泛化评估
    task_config = TaskConfig(
        causal_discovery=False,
        causal_estimation=False,
        ood=True
    )
    
    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_ood_only'
    )
    
    print("配置完成: 只执行分布外泛化评估")
    
    results = evaluate_real_datasets(config)
    return results


def example_5_discovery_and_estimation():
    """示例5: 执行因果发现 + 因果效应评估"""
    
    print("示例5: 执行因果发现 + 因果效应评估")
    print("="*40)
    
    model_config = ModelConfig(
        ols=True,
        xgboost=True,
        tabpfn_default=True
    )
    
    # 执行因果发现 + 因果效应评估
    task_config = TaskConfig(
        causal_discovery=True,
        causal_estimation=True,
        ood=False
    )
    
    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_discovery_estimation'
    )
    
    print("配置完成: 因果发现 + 因果效应评估")
    
    results = evaluate_real_datasets(config)
    return results


def example_6_custom_config():
    """示例6: 自定义配置 - 因果效应 + OOD评估"""

    print("示例6: 自定义配置 - 因果效应 + OOD评估")
    print("="*40)

    model_config = ModelConfig(
        ols=True,
        lasso=True,
        xgboost=True,
        tabpfn_default=True,
        tabpfn_mse=True
    )

    # 自定义任务配置 - 只执行因果效应和OOD评估
    task_config = TaskConfig(
        causal_discovery=False,
        causal_estimation=True,
        ood=True
    )

    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_custom'
    )

    print(f"自定义配置完成:")
    print(f"  - 因果发现评估: {task_config.causal_discovery}")
    print(f"  - 因果效应评估: {task_config.causal_estimation}")
    print(f"  - 分布外泛化评估: {task_config.ood}")

    results = evaluate_real_datasets(config)
    return results


def main():
    """主函数 - 运行所有示例"""

    print("TaskConfig使用示例")
    print("="*50)

    examples = [
        ("执行所有任务", example_1_all_tasks),
        ("只执行因果发现评估", example_2_causal_discovery_only),
        ("只执行因果效应评估", example_3_causal_estimation_only),
        ("只执行分布外泛化评估", example_4_ood_only),
        ("因果发现 + 因果效应评估", example_5_discovery_and_estimation),
        ("自定义配置 - 因果效应 + OOD", example_6_custom_config)
    ]

    print("将依次运行以下示例:")
    for i, (description, _) in enumerate(examples, 1):
        print(f"{i}. {description}")

    print("\n" + "="*50)

    # 运行所有示例
    all_results = {}
    for i, (description, example_func) in enumerate(examples, 1):
        print(f"\n>>> 运行示例 {i}: {description}")
        print("-" * 40)

        try:
            results = example_func()
            all_results[f"example_{i}"] = {
                "description": description,
                "results": results
            }
            print(f"✓ 示例 {i} 完成")
        except Exception as e:
            print(f"✗ 示例 {i} 失败: {e}")
            all_results[f"example_{i}"] = {
                "description": description,
                "error": str(e)
            }

    # 输出总体概览
    print("\n" + "="*50)
    print("所有示例执行完成")
    print("="*50)

    for example_id, example_data in all_results.items():
        description = example_data["description"]
        if "results" in example_data:
            results = example_data["results"]
            total_datasets = sum(len(type_results) for type_results in results.values())
            print(f"✓ {description}: {total_datasets} 个数据集")
        else:
            print(f"✗ {description}: 执行失败")

    return all_results


if __name__ == "__main__":
    main()
