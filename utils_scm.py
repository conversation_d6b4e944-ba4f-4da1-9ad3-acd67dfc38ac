"""
utils_scm.py - SCM工具函数库

该文件提供了SCM框架所需的各种工具函数和辅助功能：

主要功能：
1. 图操作工具：节点关系分析、图遍历、拓扑排序等
2. 采样器函数：各种随机采样器的实现
3. 节点分类：基于图结构的节点分类和关系分析
4. 边分类：基于目标节点的边分类系统
5. 图可视化：DAG图的绘制和可视化
6. 配置检测：自动检测函数配置类型

核心工具函数：
- get_parents/get_children/get_siblings/get_spouses: 节点关系获取
- graph_traversal: 通用图遍历算法
- classify_nodes_by_target_relationship_type: 节点分类
- classify_edges_by_target_relationship: 边分类
- draw_graph: 图可视化
- 各种采样器：uniform_sampler_f, class_sampler_f等

图结构分析：
- 支持父子关系、兄弟关系、配偶关系分析
- 马尔可夫毯计算
- 祖先后代关系追踪

"""

import networkx as nx
import random
import numpy as np
import scipy.stats as stats
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import torch
import os

# ============================================================================
# 通用工具函数
# ============================================================================

def handle_random_seed(seed, operation_func, *args, **kwargs):
    """统一处理随机种子的工具函数"""
    if seed is not None:
        if 'numpy' in str(type(operation_func)):
            rng = np.random.RandomState(seed)
            return operation_func(rng, *args, **kwargs)
        elif 'random' in str(operation_func.__module__):
            rng = random.Random(seed)
            return operation_func(rng, *args, **kwargs)
        else:
            # 对于其他类型，直接设置全局种子
            np.random.seed(seed)
            random.seed(seed)
            return operation_func(*args, **kwargs)
    else:
        return operation_func(*args, **kwargs)

def normalize_to_set(param):
    """将参数标准化为集合"""
    if param is None:
        return set()
    elif isinstance(param, (list, set, tuple)):
        return set(param)
    else:
        return {param}

def graph_traversal(graph, start_node, direction='forward'):
    """通用图遍历函数"""
    visited = set()
    stack = [start_node]
    result = set()
    
    get_neighbors = graph.successors if direction == 'forward' else graph.predecessors
    
    while stack:
        current = stack.pop()
        for neighbor in get_neighbors(current):
            if neighbor not in visited:
                visited.add(neighbor)
                result.add(neighbor)
                stack.append(neighbor)
    return result

# ============================================================================
# 采样器函数 
# ============================================================================

def create_sampler(dist_type, *params, seed=None):
    """统一的采样器创建函数"""
    if dist_type == 'truncnorm':
        mu, sigma = params
        return lambda: stats.truncnorm((0 - mu) / sigma, (1000000 - mu) / sigma, 
                                     loc=mu, scale=sigma).rvs(1, random_state=seed)[0]
    elif dist_type == 'beta':
        a, b = params
        return lambda: (np.random.RandomState(seed).beta(a, b) if seed is not None 
                       else np.random.beta(a, b))
    elif dist_type == 'gamma':
        a, b = params
        return lambda: (np.random.RandomState(seed).gamma(a, b) if seed is not None 
                       else np.random.gamma(a, b))
    elif dist_type == 'uniform':
        a, b = params
        return lambda: (np.random.RandomState(seed).uniform(a, b) if seed is not None 
                       else np.random.uniform(a, b))
    elif dist_type == 'uniform_int':
        a, b = params
        return lambda: round(np.random.RandomState(seed).uniform(a, b) if seed is not None 
                           else np.random.uniform(a, b))

trunc_norm_sampler_f = lambda mu, sigma, seed=None: create_sampler('truncnorm', mu, sigma, seed=seed)
beta_sampler_f = lambda a, b, seed=None: create_sampler('beta', a, b, seed=seed)
gamma_sampler_f = lambda a, b, seed=None: create_sampler('gamma', a, b, seed=seed)
uniform_sampler_f = lambda a, b, seed=None: create_sampler('uniform', a, b, seed=seed)
uniform_int_sampler_f = lambda a, b, seed=None: create_sampler('uniform_int', a, b, seed=seed)

def causes_sampler_f(num_causes, min_lb, max_lb, max_len, seed=None):
    """原因节点采样函数"""
    assert (max_lb - min_lb > 0)
    assert (max_len >= 0)  
    
    if seed is not None:
        rng = np.random.RandomState(seed)
        lb = rng.uniform(low=min_lb, high=max_lb, size=num_causes)
        ub = lb + rng.uniform(low=0.0, high=max_len, size=num_causes)
    else:
        lb = np.random.uniform(low=min_lb, high=max_lb, size=num_causes)
        ub = lb + np.random.uniform(low=0.0, high=max_len, size=num_causes)
    return lb, ub

def class_sampler_f(min_: int, max_: int, seed=None):
    """类别采样器函数"""
    def s():
        rng = random.Random(seed) if seed is not None else random
        if rng.random() > 0.5:
            return uniform_int_sampler_f(min_, max_, seed)()
        return 2
    return s

def randomize_classes(x, num_classes, seed=None):
    """随机化类别标签"""
    classes = torch.arange(0, num_classes, device=x.device)
    
    if seed is not None:
        rng_state = torch.get_rng_state()
        cuda_rng_state = torch.cuda.get_rng_state() if torch.cuda.is_available() else None
        
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
        
        random_classes = torch.randperm(num_classes, device=x.device).type(x.type())
        
        torch.set_rng_state(rng_state)
        if cuda_rng_state is not None:
            torch.cuda.set_rng_state(cuda_rng_state)
    else:
        random_classes = torch.randperm(num_classes, device=x.device).type(x.type())
    
    x = ((x.unsqueeze(-1) == classes) * random_classes).sum(-1)
    return x

# ============================================================================
# 节点关系计算 
# ============================================================================

def get_exclusive_node_relationships(graph, target_node):
    """
    简化的节点关系分类：父节点、子节点、配偶节点、其他节点
    优先级：父节点 > 子节点 > 配偶节点 > 其他节点
    """
    all_nodes = set(graph.nodes())
    classified_nodes = {target_node}

    # 1. 父节点（最高优先级）
    parents = set(graph.predecessors(target_node))
    classified_nodes.update(parents)

    # 2. 子节点（第二优先级）
    children = set(graph.successors(target_node))
    classified_nodes.update(children)

    # 3. 配偶节点（第三优先级）- 与目标节点有共同子节点的节点
    spouses = set()
    for child in children:
        spouses.update(set(graph.predecessors(child)) - classified_nodes)
    classified_nodes.update(spouses)

    # 4. 其他节点（最低优先级）- 剩余的所有节点
    others = all_nodes - classified_nodes

    return {
        'parents': parents,
        'children': children,
        'spouses': spouses,
        'others': others
    }


def get_parents(graph, node):
    """获取图中一个节点的父节点集合"""
    return set(graph.predecessors(node))

def get_children(graph, node):
    """获取图中一个节点的子节点集合"""
    return set(graph.successors(node))

def get_spouses(graph, node):
    """获取图中一个节点的配偶节点集合（共同孩子的其他父母）"""
    relationships = get_exclusive_node_relationships(graph, node)
    return relationships['spouses']

def get_all_descendants(graph, node):
    """获取图中一个节点的所有后代节点"""
    return graph_traversal(graph, node, direction='forward')

def get_grandparents(graph, node):
    """获取图中一个节点的祖父节点集合"""
    grandparents = set()
    for parent in graph.predecessors(node):
        grandparents.update(graph.predecessors(parent))
    return grandparents

def get_all_antecedents(graph, node):
    """获取图中一个节点的所有祖先节点"""
    return graph_traversal(graph, node, direction='backward')

def get_siblings(graph, node):
    """获取图中一个节点的兄弟节点集合"""
    siblings = set()
    for parent in graph.predecessors(node):
        siblings.update(graph.successors(parent))
    siblings.discard(node)  # 移除自己
    return siblings

# ============================================================================
# 边和节点分类函数
# ============================================================================

def classify_edges_by_target_relationship(graph, target_node):
    """根据目标节点对图中所有边进行分类"""
    if target_node not in graph.nodes():
        raise ValueError(f"目标节点 {target_node} 不在图中")

    # 获取目标节点的各种关系节点
    relationships = get_exclusive_node_relationships(graph, target_node)
    parents = relationships['parents']
    children = relationships['children']
    spouses = relationships['spouses']

    # 初始化边分类字典
    edge_classification = {
        'Yparent_Y': [],
        'Y_Ychild': [],
        'Yspouse_Ychild': [],
        'Other': []
    }

    # 遍历图中所有边进行分类
    for edge in graph.edges():
        source, edge_target = edge
        edge_classified = False

        # 1. Yparent_Y: 父节点到目标节点的边
        if source in parents and edge_target == target_node:
            edge_classification['Yparent_Y'].append(edge)
            edge_classified = True
        # 2. Y_Ychild: 目标节点到子节点的边
        elif source == target_node and edge_target in children:
            edge_classification['Y_Ychild'].append(edge)
            edge_classified = True
        # 3. Yspouse_Ychild: 配偶节点到共同子节点的边
        elif source in spouses and edge_target in children:
            if edge_target in graph.successors(target_node) and edge_target in graph.successors(source):
                edge_classification['Yspouse_Ychild'].append(edge)
                edge_classified = True

        # 4. Other: 其他所有边
        if not edge_classified:
            edge_classification['Other'].append(edge)

    return edge_classification

def classify_nodes_by_target_relationship_type(graph, target_node):
    """根据目标节点对图中所有节点进行类型分类"""
    if target_node not in graph.nodes():
        raise ValueError(f"目标节点 {target_node} 不在图中")

    # 获取Y的子节点
    children = get_children(graph, target_node)
    all_nodes = set(graph.nodes())

    # 分类节点
    node_classification = {
        'target': [target_node],
        'target_child': list(children),
        'Other_type': list(all_nodes - {target_node} - children)
    }

    return node_classification

def detect_node_type_config(custom_functions, target_node):
    """检测自定义函数配置是否使用节点类型级配置"""
    if custom_functions is None:
        return False

    # 节点类型级配置的关键字
    node_type_keys = {'target', 'target_child', 'Other_type'}
    config_keys = set(custom_functions.keys())
    has_node_type_keys = bool(config_keys & node_type_keys)
    has_specific_node_keys = target_node in config_keys if target_node else False

    # 如果同时包含节点类型和具体节点名称，优先使用节点类型配置
    if has_node_type_keys:
        return True
    elif has_specific_node_keys:
        return False
    else:
        # 如果都没有，检查是否有边级配置
        edge_type_keys = {'Yparent_Y', 'Y_Ychild', 'Yspouse_Ychild', 'Yparent_Ysibling', 'Ygrandparent_Yparent'}
        has_edge_type_keys = bool(config_keys & edge_type_keys)
        return not has_edge_type_keys

def get_node_type_for_node(node, node_classification):
    """获取指定节点的类型"""
    for node_type, nodes in node_classification.items():
        if node in nodes:
            return node_type
    return 'Other_type'

def get_edge_function_config(edge, edge_classification, custom_functions):
    """根据边的分类获取对应的函数配置"""
    if custom_functions is None:
        return None

    # 检查是否是新的边级配置格式
    edge_type_keys = ['Yparent_Y', 'Y_Ychild', 'Yspouse_Ychild', 'Yparent_Ysibling', 'Ygrandparent_Yparent', 'Other']
    is_edge_based_config = any(key in custom_functions for key in edge_type_keys)

    if not is_edge_based_config:
        # 如果是旧的节点级配置，返回目标节点的配置
        _, target = edge
        return custom_functions.get(target, None)

    # 新的边级配置：找到该边属于哪个类型
    for edge_type, edges in edge_classification.items():
        if edge in edges:
            return custom_functions.get(edge_type, None)

    # 如果没有找到分类，使用Other类别的配置
    return custom_functions.get('Other', None)

# ============================================================================
# 图形绘制函数 
# ============================================================================

def draw_graph(G, path, target_node=None, intervention_nodes=None, unobserved_nodes=None,
               selected_features=None, assignment=None, scm=None, model_results=None):
    """
    绘制有向图，并用特殊颜色标记目标节点、干预/扰动节点和被选择的特征节点
    """
    plt.figure(figsize=(8, 6))
    pos = nx.nx_agraph.graphviz_layout(G, prog="dot")

    # 使用统一的参数标准化函数
    intervention_nodes_set = normalize_to_set(intervention_nodes)
    unobserved_nodes_set = normalize_to_set(unobserved_nodes)
    selected_features_set = normalize_to_set(selected_features)

    # 准备节点颜色列表
    node_colors = []
    for node in G.nodes():
        if node == target_node:
            node_colors.append('#dbc4ce')
        elif node in intervention_nodes_set:
            node_colors.append('#f3e6bd')
        elif node in selected_features_set:
            node_colors.append('#a7cbd3')
        else:
            node_colors.append('#a9afcb')

    # 根据节点数量自适应调整字体大小和节点大小
    num_nodes = len(G.nodes())
    if num_nodes <= 30:
        font_size, node_size = 10, 300
    elif num_nodes <= 50:
        font_size, node_size = 8, 200
    elif num_nodes <= 100:
        font_size, node_size = 5, 150
    else:
        font_size, node_size = 3, 100

    # 绘制基本图形
    nx.draw(G, pos, with_labels=True, node_size=node_size,
            node_color=node_colors, edge_color='gray', arrows=True,
            font_color='white', font_weight='bold', font_size=font_size)

    # 为不可观测的节点添加阴影效果和虚线边框
    for node in unobserved_nodes_set:
        if node in G.nodes():
            plt.scatter(pos[node][0], pos[node][1], s=600, color='gray', alpha=0.5)
            circle = plt.Circle((pos[node][0], pos[node][1]), radius=25, fill=False,
                              linestyle='dashed', edgecolor='black', linewidth=2)
            plt.gca().add_patch(circle)

    # 生成详细信息并保存到txt文件
    if assignment is not None:
        _save_graph_info(G, path, assignment, scm, model_results)

    plt.savefig(path, dpi=300, bbox_inches='tight')
    plt.close()

def _save_graph_info(G, path, assignment, scm, model_results):
    """保存图形详细信息到txt文件的辅助函数"""
    info_lines = []

    # 获取SNR验证结果（如果有）
    snr_validation_results = None
    if scm is not None and hasattr(scm, 'snr_validation_results'):
        snr_validation_results = scm.snr_validation_results

    # 生成节点信息
    for node in G.nodes():
        parents = list(G.predecessors(node))
        if not parents:
            continue

        node_info = f"parents: {parents} -> child: {node}"
        if node in assignment:
            model_info = assignment[node]['assignment'].get_model_info()
            f_layers = model_info.get('f_layers', [])
            f_layer_strs = []
            for layer in f_layers:
                if layer['type'] == 'Linear':
                    w = layer['weight']
                    b = layer['bias']
                    w_str = str(w[:5]) if len(w) > 5 else str(w)
                    b_str = str(b[:5]) if len(b) > 5 else str(b)
                    f_layer_strs.append(f"Linear\nw={w_str}\nb={b_str}")
                else:
                    f_layer_strs.append(layer['type'])
            f_layers_info = '\n---\n'.join(f_layer_strs)
            g_str = model_info.get('g_function', '')
            init_std = model_info.get('init_std', 'N/A')
            noise_std = model_info.get('noise_std', 'N/A')
            node_info += f"\n  f函数:\n{f_layers_info}"
            node_info += f"\n  g函数: {g_str}"
            node_info += f"\n  init_std: {init_std}"
            node_info += f"\n  noise_std: {noise_std}"

            # 添加SNR验证结果信息
            if snr_validation_results is not None:
                _add_snr_info(node_info, node, snr_validation_results)

        info_lines.append(node_info)

    # 添加整体SNR验证摘要和模型评估结果
    if snr_validation_results is not None and 'summary' in snr_validation_results:
        _add_snr_summary(info_lines, snr_validation_results['summary'])

    if model_results is not None and len(model_results) > 0:
        _add_model_results(info_lines, model_results)

    # 保存参数信息到txt
    txt_path = os.path.splitext(path)[0] + '.txt'
    with open(txt_path, 'w', encoding='utf-8') as f:
        f.write('\n\n'.join(info_lines))

def _add_snr_info(node_info, node, snr_validation_results):
    """添加SNR验证信息的辅助函数"""
    signal_var = snr_validation_results.get('signal_var', {}).get(node, 'N/A')
    actual_noise_var = snr_validation_results.get('actual_noise_var', {}).get(node, 'N/A')
    actual_snr = snr_validation_results.get('actual_snr', {}).get(node, 'N/A')
    target_snr = snr_validation_results.get('target_snr', {}).get(node, 'N/A')

    signal_std = 'N/A'
    actual_noise_std = 'N/A'
    if signal_var != 'N/A' and isinstance(signal_var, (int, float)):
        signal_std = f"{signal_var**0.5:.6f}"
        signal_var = f"{signal_var:.6f}"
    if actual_noise_var != 'N/A' and isinstance(actual_noise_var, (int, float)):
        actual_noise_std = f"{actual_noise_var**0.5:.6f}"
        actual_noise_var = f"{actual_noise_var:.6f}"

    node_info += f"\n  === SNR验证结果 ==="
    node_info += f"\n  实际信号方差: {signal_var}"
    node_info += f"\n  实际信号标准差: {signal_std}"
    node_info += f"\n  实际噪声方差: {actual_noise_var}"
    node_info += f"\n  实际噪声标准差: {actual_noise_std}"
    node_info += f"\n  实际SNR: {actual_snr}"
    node_info += f"\n  目标SNR: {target_snr}"

def _add_snr_summary(info_lines, summary):
    """添加SNR验证摘要的辅助函数"""
    summary_info = "\n=== 整体SNR验证摘要 ==="
    summary_info += f"\n平均绝对误差: {summary.get('mean_absolute_error', 'N/A')}"
    summary_info += f"\n最大绝对误差: {summary.get('max_absolute_error', 'N/A')}"
    summary_info += f"\n平均相对误差: {summary.get('mean_relative_error', 'N/A')}"
    summary_info += f"\n最大相对误差: {summary.get('max_relative_error', 'N/A')}"
    summary_info += f"\n验证节点数: {summary.get('num_nodes', 'N/A')}"
    info_lines.append(summary_info)

def _add_model_results(info_lines, model_results):
    """添加模型评估结果的辅助函数"""
    model_info = "\n=== 模型评估结果 ==="

    # 按模型类型分组
    models_by_type = {}
    for result in model_results:
        model_type = result.get('model_type', 'unknown')
        if model_type not in models_by_type:
            models_by_type[model_type] = []
        models_by_type[model_type].append(result)

    for model_type, results in models_by_type.items():
        model_info += f"\n\n--- {model_type.upper()} 模型 ---"
        for result in results:
            dataset_name = result.get('dataset', 'unknown')
            config_name = result.get('use_snr', 'unknown')
            model_info += f"\n数据集: {dataset_name}, 配置: {config_name}"

            # R2指标
            model_info += f"\n  R² - Train: {result.get('r2_train', 'N/A'):.4f}, Test: {result.get('r2_test', 'N/A'):.4f}, Intervention: {result.get('r2_intv', 'N/A'):.4f}"

            # RMSE指标
            model_info += f"\n  RMSE - Train: {result.get('rmse_train', 'N/A'):.4f}, Test: {result.get('rmse_test', 'N/A'):.4f}, Intervention: {result.get('rmse_intv', 'N/A'):.4f}"

            # MAPE指标
            model_info += f"\n  MAPE - Train: {result.get('mape_train', 'N/A'):.2f}%, Test: {result.get('mape_test', 'N/A'):.2f}%, Intervention: {result.get('mape_intv', 'N/A'):.2f}%"

    info_lines.append(model_info)
