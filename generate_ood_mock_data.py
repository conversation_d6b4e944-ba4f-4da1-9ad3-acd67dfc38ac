"""
generate_ood_mock_data.py - 生成OOD数据集的模拟数据

根据real_data_evaluator.py中create_sample_ood_configs()的配置，
生成符合要求的模拟数据，用于验证模型评估代码的有效性。
"""

import numpy as np
import pandas as pd
import os
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler


def create_office_home_data():
    """生成Office-Home数据集的模拟数据"""
    
    print("生成Office-Home数据集...")
    
    # 创建目录
    os.makedirs("data/office_home", exist_ok=True)
    
    # 设置随机种子
    np.random.seed(42)
    
    # 生成基础特征和标签
    n_samples_train = 2000
    n_samples_test = 500
    n_features = 10
    n_classes = 4  # art, clipart, product, real_world
    
    # 生成训练数据 (real_world域)
    X_train, y_train = make_classification(
        n_samples=n_samples_train,
        n_features=n_features,
        n_classes=n_classes,
        n_informative=8,
        n_redundant=2,
        n_clusters_per_class=1,
        random_state=42
    )
    
    # 标准化特征
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    
    # 创建训练数据DataFrame
    feature_columns = [f"feature_{i+1}" for i in range(n_features)]
    train_df = pd.DataFrame(X_train, columns=feature_columns)
    train_df['category'] = y_train
    
    # 保存训练数据
    train_df.to_csv("data/office_home/real_world_train.csv", index=False)
    print(f"  ✓ 训练数据: {train_df.shape}")
    
    # 生成测试数据 - 不同域有不同的分布偏移
    domains = ['art', 'clipart', 'product']
    domain_shifts = [
        {'mean_shift': 0.5, 'std_scale': 1.2},   # art域
        {'mean_shift': -0.3, 'std_scale': 0.8},  # clipart域
        {'mean_shift': 0.2, 'std_scale': 1.5}    # product域
    ]
    
    for domain, shift in zip(domains, domain_shifts):
        # 生成测试数据
        X_test, y_test = make_classification(
            n_samples=n_samples_test,
            n_features=n_features,
            n_classes=n_classes,
            n_informative=8,
            n_redundant=2,
            n_clusters_per_class=1,
            random_state=42 + hash(domain) % 100
        )
        
        # 应用域偏移
        X_test = scaler.transform(X_test)
        X_test = X_test + shift['mean_shift']
        X_test = X_test * shift['std_scale']
        
        # 创建测试数据DataFrame
        test_df = pd.DataFrame(X_test, columns=feature_columns)
        test_df['category'] = y_test
        
        # 保存测试数据
        test_df.to_csv(f"data/office_home/{domain}_test.csv", index=False)
        print(f"  ✓ {domain}测试数据: {test_df.shape}")


def create_terra_incognita_data():
    """生成Terra Incognita数据集的模拟数据"""
    
    print("生成Terra Incognita数据集...")
    
    # 创建目录
    os.makedirs("data/terra", exist_ok=True)
    
    # 设置随机种子
    np.random.seed(123)
    
    # 生成基础特征和标签
    n_samples_train = 1500
    n_samples_test = 400
    n_features = 15  # 环境特征
    n_classes = 5    # 物种类别
    
    # 生成训练数据 (location_100)
    X_train, y_train = make_classification(
        n_samples=n_samples_train,
        n_features=n_features,
        n_classes=n_classes,
        n_informative=12,
        n_redundant=3,
        n_clusters_per_class=2,
        random_state=123
    )
    
    # 标准化特征
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    
    # 创建训练数据DataFrame
    feature_columns = [f"env_{i+1}" for i in range(n_features)]
    train_df = pd.DataFrame(X_train, columns=feature_columns)
    train_df['species'] = y_train
    
    # 保存训练数据
    train_df.to_csv("data/terra/location_100_train.csv", index=False)
    print(f"  ✓ 训练数据: {train_df.shape}")
    
    # 生成测试数据 - 不同地理位置有不同的环境特征分布
    locations = ['location_38', 'location_43', 'location_46', 'location_47']
    location_shifts = [
        {'mean_shift': np.array([0.8, -0.5, 0.3, 0.2, -0.4, 0.6, -0.3, 0.5, 0.1, -0.2, 0.4, -0.6, 0.2, 0.3, -0.1]), 'std_scale': 1.1},
        {'mean_shift': np.array([-0.6, 0.4, -0.2, 0.7, 0.3, -0.5, 0.8, -0.1, 0.4, 0.6, -0.3, 0.2, -0.4, 0.1, 0.5]), 'std_scale': 0.9},
        {'mean_shift': np.array([0.2, 0.6, -0.8, 0.1, 0.5, 0.3, -0.4, 0.7, -0.2, 0.4, 0.1, -0.5, 0.6, -0.3, 0.2]), 'std_scale': 1.3},
        {'mean_shift': np.array([-0.3, 0.1, 0.5, -0.7, 0.2, 0.8, 0.4, -0.6, 0.3, -0.1, 0.7, 0.2, -0.4, 0.5, -0.2]), 'std_scale': 0.8}
    ]
    
    for location, shift in zip(locations, location_shifts):
        # 生成测试数据
        X_test, y_test = make_classification(
            n_samples=n_samples_test,
            n_features=n_features,
            n_classes=n_classes,
            n_informative=12,
            n_redundant=3,
            n_clusters_per_class=2,
            random_state=123 + hash(location) % 100
        )
        
        # 应用地理位置偏移
        X_test = scaler.transform(X_test)
        X_test = X_test + shift['mean_shift']
        X_test = X_test * shift['std_scale']
        
        # 创建测试数据DataFrame
        test_df = pd.DataFrame(X_test, columns=feature_columns)
        test_df['species'] = y_test
        
        # 保存测试数据
        test_df.to_csv(f"data/terra/{location}_test.csv", index=False)
        print(f"  ✓ {location}测试数据: {test_df.shape}")


def verify_data_structure():
    """验证生成的数据结构是否符合配置要求"""
    
    print("\n验证数据结构...")
    
    # 验证Office-Home数据
    print("\nOffice-Home数据集:")
    
    # 检查训练数据
    train_path = "data/office_home/real_world_train.csv"
    if os.path.exists(train_path):
        df = pd.read_csv(train_path)
        print(f"  训练数据: {df.shape}")
        print(f"  特征列: {list(df.columns[:-1])}")
        print(f"  目标列: {df.columns[-1]}")
        print(f"  类别分布: {df['category'].value_counts().to_dict()}")
    
    # 检查测试数据
    test_domains = ['art', 'clipart', 'product']
    for domain in test_domains:
        test_path = f"data/office_home/{domain}_test.csv"
        if os.path.exists(test_path):
            df = pd.read_csv(test_path)
            print(f"  {domain}测试数据: {df.shape}")
    
    # 验证Terra Incognita数据
    print("\nTerra Incognita数据集:")
    
    # 检查训练数据
    train_path = "data/terra/location_100_train.csv"
    if os.path.exists(train_path):
        df = pd.read_csv(train_path)
        print(f"  训练数据: {df.shape}")
        print(f"  特征列: {list(df.columns[:-1])}")
        print(f"  目标列: {df.columns[-1]}")
        print(f"  物种分布: {df['species'].value_counts().to_dict()}")
    
    # 检查测试数据
    test_locations = ['location_38', 'location_43', 'location_46', 'location_47']
    for location in test_locations:
        test_path = f"data/terra/{location}_test.csv"
        if os.path.exists(test_path):
            df = pd.read_csv(test_path)
            print(f"  {location}测试数据: {df.shape}")


def main():
    """主函数"""
    
    print("生成OOD数据集的模拟数据")
    print("="*50)
    
    # 生成Office-Home数据
    create_office_home_data()
    
    print()
    
    # 生成Terra Incognita数据
    create_terra_incognita_data()
    
    # 验证数据结构
    verify_data_structure()
    
    print("\n" + "="*50)
    print("模拟数据生成完成！")
    print("\n生成的文件:")
    print("Office-Home数据集:")
    print("  - data/office_home/real_world_train.csv")
    print("  - data/office_home/art_test.csv")
    print("  - data/office_home/clipart_test.csv")
    print("  - data/office_home/product_test.csv")
    print("\nTerra Incognita数据集:")
    print("  - data/terra/location_100_train.csv")
    print("  - data/terra/location_38_test.csv")
    print("  - data/terra/location_43_test.csv")
    print("  - data/terra/location_46_test.csv")
    print("  - data/terra/location_47_test.csv")
    print("\n现在可以运行 real_data_evaluator.py 来验证模型评估代码！")


if __name__ == "__main__":
    main()
