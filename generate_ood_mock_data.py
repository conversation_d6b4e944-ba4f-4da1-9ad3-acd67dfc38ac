"""
generate_ood_mock_data.py - 生成OOD和因果推断数据集的模拟数据

根据real_data_evaluator.py中的配置，生成符合要求的模拟数据，
用于验证模型评估代码的有效性。
"""

import numpy as np
import pandas as pd
import os
from sklearn.datasets import make_classification
from sklearn.preprocessing import StandardScaler


def create_office_home_data():
    """生成Office-Home数据集的模拟数据"""
    
    print("生成Office-Home数据集...")
    
    # 创建目录
    os.makedirs("data/office_home", exist_ok=True)
    
    # 设置随机种子
    np.random.seed(42)
    
    # 生成基础特征和标签
    n_samples_train = 2000
    n_samples_test = 500
    n_features = 10
    n_classes = 4  # art, clipart, product, real_world
    
    # 生成训练数据 (real_world域)
    X_train, y_train = make_classification(
        n_samples=n_samples_train,
        n_features=n_features,
        n_classes=n_classes,
        n_informative=8,
        n_redundant=2,
        n_clusters_per_class=1,
        random_state=42
    )
    
    # 标准化特征
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    
    # 创建训练数据DataFrame
    feature_columns = [f"feature_{i+1}" for i in range(n_features)]
    train_df = pd.DataFrame(X_train, columns=feature_columns)
    train_df['category'] = y_train
    
    # 保存训练数据
    train_df.to_csv("data/office_home/real_world_train.csv", index=False)
    print(f"  ✓ 训练数据: {train_df.shape}")
    
    # 生成测试数据 - 不同域有不同的分布偏移
    domains = ['art', 'clipart', 'product']
    domain_shifts = [
        {'mean_shift': 0.5, 'std_scale': 1.2},   # art域
        {'mean_shift': -0.3, 'std_scale': 0.8},  # clipart域
        {'mean_shift': 0.2, 'std_scale': 1.5}    # product域
    ]
    
    for domain, shift in zip(domains, domain_shifts):
        # 生成测试数据
        X_test, y_test = make_classification(
            n_samples=n_samples_test,
            n_features=n_features,
            n_classes=n_classes,
            n_informative=8,
            n_redundant=2,
            n_clusters_per_class=1,
            random_state=42 + hash(domain) % 100
        )
        
        # 应用域偏移
        X_test = scaler.transform(X_test)
        X_test = X_test + shift['mean_shift']
        X_test = X_test * shift['std_scale']
        
        # 创建测试数据DataFrame
        test_df = pd.DataFrame(X_test, columns=feature_columns)
        test_df['category'] = y_test
        
        # 保存测试数据
        test_df.to_csv(f"data/office_home/{domain}_test.csv", index=False)
        print(f"  ✓ {domain}测试数据: {test_df.shape}")


def create_terra_incognita_data():
    """生成Terra Incognita数据集的模拟数据"""

    print("生成Terra Incognita数据集...")

    # 创建目录
    os.makedirs("data/terra", exist_ok=True)

    # 设置随机种子
    np.random.seed(123)

    # 生成基础特征和标签
    n_samples_train = 1500
    n_samples_test = 400
    n_features = 15  # 环境特征
    n_classes = 5    # 物种类别

    # 生成训练数据 (location_100)
    X_train, y_train = make_classification(
        n_samples=n_samples_train,
        n_features=n_features,
        n_classes=n_classes,
        n_informative=12,
        n_redundant=3,
        n_clusters_per_class=2,
        random_state=123
    )

    # 标准化特征
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)

    # 创建训练数据DataFrame
    feature_columns = [f"env_{i+1}" for i in range(n_features)]
    train_df = pd.DataFrame(X_train, columns=feature_columns)
    train_df['species'] = y_train

    # 保存训练数据
    train_df.to_csv("data/terra/location_100_train.csv", index=False)
    print(f"  ✓ 训练数据: {train_df.shape}")

    # 生成测试数据 - 不同地理位置有不同的环境特征分布
    locations = ['location_38', 'location_43', 'location_46', 'location_47']
    location_shifts = [
        {'mean_shift': np.array([0.8, -0.5, 0.3, 0.2, -0.4, 0.6, -0.3, 0.5, 0.1, -0.2, 0.4, -0.6, 0.2, 0.3, -0.1]), 'std_scale': 1.1},
        {'mean_shift': np.array([-0.6, 0.4, -0.2, 0.7, 0.3, -0.5, 0.8, -0.1, 0.4, 0.6, -0.3, 0.2, -0.4, 0.1, 0.5]), 'std_scale': 0.9},
        {'mean_shift': np.array([0.2, 0.6, -0.8, 0.1, 0.5, 0.3, -0.4, 0.7, -0.2, 0.4, 0.1, -0.5, 0.6, -0.3, 0.2]), 'std_scale': 1.3},
        {'mean_shift': np.array([-0.3, 0.1, 0.5, -0.7, 0.2, 0.8, 0.4, -0.6, 0.3, -0.1, 0.7, 0.2, -0.4, 0.5, -0.2]), 'std_scale': 0.8}
    ]

    for location, shift in zip(locations, location_shifts):
        # 生成测试数据
        X_test, y_test = make_classification(
            n_samples=n_samples_test,
            n_features=n_features,
            n_classes=n_classes,
            n_informative=12,
            n_redundant=3,
            n_clusters_per_class=2,
            random_state=123 + hash(location) % 100
        )

        # 应用地理位置偏移
        X_test = scaler.transform(X_test)
        X_test = X_test + shift['mean_shift']
        X_test = X_test * shift['std_scale']

        # 创建测试数据DataFrame
        test_df = pd.DataFrame(X_test, columns=feature_columns)
        test_df['species'] = y_test

        # 保存测试数据
        test_df.to_csv(f"data/terra/{location}_test.csv", index=False)
        print(f"  ✓ {location}测试数据: {test_df.shape}")


def create_jobs_data():
    """生成Jobs数据集的模拟数据 - 基于LaLonde就业培训实验"""

    print("生成Jobs数据集...")

    # 创建目录
    os.makedirs("data/jobs", exist_ok=True)

    # 设置随机种子
    np.random.seed(456)

    n_samples = 2000

    # 生成基础特征
    # age: 年龄 (17-55)
    age = np.random.normal(25, 7, n_samples)
    age = np.clip(age, 17, 55)

    # education: 教育年限 (6-16)
    education = np.random.normal(10, 2.5, n_samples)
    education = np.clip(education, 6, 16)

    # black: 是否黑人 (0/1)
    black = np.random.binomial(1, 0.4, n_samples)

    # hispanic: 是否西班牙裔 (0/1)
    hispanic = np.random.binomial(1, 0.2, n_samples)

    # married: 是否已婚 (0/1)
    married = np.random.binomial(1, 0.3, n_samples)

    # nodegree: 是否无学位 (0/1)
    nodegree = np.random.binomial(1, 0.6, n_samples)

    # re74: 1974年收入 (0-40000)
    re74 = np.random.exponential(3000, n_samples)
    re74 = np.clip(re74, 0, 40000)

    # re75: 1975年收入 (0-40000)
    re75 = np.random.exponential(3500, n_samples)
    re75 = np.clip(re75, 0, 40000)

    # 生成处理变量 (treat) - 基于特征的倾向性评分
    propensity_score = (
        -0.5 +
        0.02 * age +
        0.1 * education +
        0.3 * black +
        0.2 * hispanic +
        -0.4 * married +
        0.5 * nodegree +
        -0.00001 * re74 +
        -0.00001 * re75
    )
    propensity_prob = 1 / (1 + np.exp(-propensity_score))
    treat = np.random.binomial(1, propensity_prob, n_samples)

    # 生成结果变量 (re78) - 1978年收入，受处理影响
    treatment_effect = 1500  # 平均处理效应
    noise = np.random.normal(0, 2000, n_samples)

    re78 = (
        2000 +  # 基础收入
        50 * age +
        300 * education +
        -800 * black +
        -400 * hispanic +
        600 * married +
        -1000 * nodegree +
        0.3 * re74 +
        0.4 * re75 +
        treatment_effect * treat +  # 处理效应
        noise
    )
    re78 = np.clip(re78, 0, 60000)

    # 创建DataFrame
    jobs_df = pd.DataFrame({
        'age': age,
        'education': education,
        'black': black,
        'hispanic': hispanic,
        'married': married,
        'nodegree': nodegree,
        're74': re74,
        're75': re75,
        'treat': treat,
        're78': re78
    })

    # 保存数据
    jobs_df.to_csv("data/jobs/jobs.csv", index=False)
    print(f"  ✓ Jobs数据: {jobs_df.shape}")
    print(f"  ✓ 处理组比例: {treat.mean():.3f}")
    print(f"  ✓ 平均处理效应: {jobs_df[jobs_df['treat']==1]['re78'].mean() - jobs_df[jobs_df['treat']==0]['re78'].mean():.2f}")


def create_lalonde_data():
    """生成LaLonde数据集的模拟数据 - 经典因果推断数据集"""

    print("生成LaLonde数据集...")

    # 创建目录
    os.makedirs("data/lalonde", exist_ok=True)

    # 设置随机种子
    np.random.seed(789)

    n_samples = 1800

    # 生成基础特征
    # age: 年龄 (16-55)
    age = np.random.normal(25, 8, n_samples)
    age = np.clip(age, 16, 55)

    # educ: 教育年限 (3-18)
    educ = np.random.normal(10, 3, n_samples)
    educ = np.clip(educ, 3, 18)

    # black: 是否黑人 (0/1)
    black = np.random.binomial(1, 0.45, n_samples)

    # hisp: 是否西班牙裔 (0/1)
    hisp = np.random.binomial(1, 0.15, n_samples)

    # married: 是否已婚 (0/1)
    married = np.random.binomial(1, 0.25, n_samples)

    # nodegr: 是否无高中学历 (0/1)
    nodegr = np.random.binomial(1, 0.7, n_samples)

    # re74: 1974年收入 (0-35000)
    re74 = np.random.exponential(2500, n_samples)
    re74 = np.clip(re74, 0, 35000)

    # re75: 1975年收入 (0-35000)
    re75 = np.random.exponential(2800, n_samples)
    re75 = np.clip(re75, 0, 35000)

    # u74: 1974年是否失业 (0/1)
    u74 = np.random.binomial(1, 0.6, n_samples)

    # u75: 1975年是否失业 (0/1)
    u75 = np.random.binomial(1, 0.55, n_samples)

    # 生成处理变量 (treat) - 基于特征的倾向性评分
    propensity_score = (
        -1.0 +
        0.03 * age +
        0.08 * educ +
        0.4 * black +
        0.3 * hisp +
        -0.5 * married +
        0.6 * nodegr +
        -0.00002 * re74 +
        -0.00002 * re75 +
        0.8 * u74 +
        0.7 * u75
    )
    propensity_prob = 1 / (1 + np.exp(-propensity_score))
    treat = np.random.binomial(1, propensity_prob, n_samples)

    # 生成结果变量 (re78) - 1978年收入，受处理影响
    treatment_effect = 1800  # 平均处理效应
    noise = np.random.normal(0, 2500, n_samples)

    re78 = (
        1500 +  # 基础收入
        60 * age +
        400 * educ +
        -1000 * black +
        -600 * hisp +
        800 * married +
        -1200 * nodegr +
        0.25 * re74 +
        0.35 * re75 +
        -2000 * u74 +
        -1800 * u75 +
        treatment_effect * treat +  # 处理效应
        noise
    )
    re78 = np.clip(re78, 0, 50000)

    # 创建DataFrame
    lalonde_df = pd.DataFrame({
        'age': age,
        'educ': educ,
        'black': black,
        'hisp': hisp,
        'married': married,
        'nodegr': nodegr,
        're74': re74,
        're75': re75,
        'u74': u74,
        'u75': u75,
        'treat': treat,
        're78': re78
    })

    # 保存数据
    lalonde_df.to_csv("data/lalonde/lalonde.csv", index=False)
    print(f"  ✓ LaLonde数据: {lalonde_df.shape}")
    print(f"  ✓ 处理组比例: {treat.mean():.3f}")
    print(f"  ✓ 平均处理效应: {lalonde_df[lalonde_df['treat']==1]['re78'].mean() - lalonde_df[lalonde_df['treat']==0]['re78'].mean():.2f}")


def create_jobs_data():
    """生成Jobs数据集的模拟数据"""

    print("生成Jobs数据集...")

    # 创建目录
    os.makedirs("data/jobs", exist_ok=True)

    # 设置随机种子
    np.random.seed(456)

    n_samples = 2000

    # 生成基础特征
    age = np.random.normal(35, 10, n_samples).clip(18, 65)
    education = np.random.normal(12, 3, n_samples).clip(6, 20)
    black = np.random.binomial(1, 0.3, n_samples)
    hispanic = np.random.binomial(1, 0.2, n_samples)
    married = np.random.binomial(1, 0.6, n_samples)
    nodegree = np.random.binomial(1, 0.4, n_samples)

    # 生成历史收入（1974, 1975年）
    re74 = np.random.exponential(8000, n_samples) * (1 + 0.1 * education/12) * (1 - 0.2 * black) * (1 - 0.1 * hispanic)
    re75 = re74 * np.random.normal(1.05, 0.2, n_samples).clip(0.5, 2.0)

    # 生成处理变量（参加就业培训）
    # 处理分配倾向与特征相关
    propensity_score = (
        -2.0 +
        0.05 * age +
        0.1 * education +
        0.3 * black +
        0.2 * hispanic +
        -0.4 * married +
        0.5 * nodegree +
        -0.0001 * re74 +
        -0.0001 * re75
    )
    treat_prob = 1 / (1 + np.exp(-propensity_score))
    treat = np.random.binomial(1, treat_prob, n_samples)

    # 生成结果变量（1978年收入）
    # 处理效应：培训对收入的影响
    treatment_effect = 1500 + 200 * education/12 - 300 * black - 200 * hispanic

    re78 = (
        2000 +  # 基础收入
        100 * age +
        500 * education +
        -1000 * black +
        -800 * hispanic +
        800 * married +
        -600 * nodegree +
        0.3 * re74 +
        0.4 * re75 +
        treat * treatment_effect +  # 处理效应
        np.random.normal(0, 2000, n_samples)  # 噪声
    ).clip(0, None)  # 收入不能为负

    # 创建DataFrame
    jobs_df = pd.DataFrame({
        'age': age.astype(int),
        'education': education.round(1),
        'black': black,
        'hispanic': hispanic,
        'married': married,
        'nodegree': nodegree,
        're74': re74.round(2),
        're75': re75.round(2),
        'treat': treat,
        're78': re78.round(2)
    })

    # 保存数据
    jobs_df.to_csv("data/jobs/jobs.csv", index=False)
    print(f"  ✓ Jobs数据: {jobs_df.shape}")
    print(f"  处理组比例: {treat.mean():.3f}")
    print(f"  平均处理效应: {jobs_df[jobs_df['treat']==1]['re78'].mean() - jobs_df[jobs_df['treat']==0]['re78'].mean():.2f}")


def create_lalonde_data():
    """生成LaLonde数据集的模拟数据"""

    print("生成LaLonde数据集...")

    # 创建目录
    os.makedirs("data/lalonde", exist_ok=True)

    # 设置随机种子
    np.random.seed(789)

    n_samples = 1800

    # 生成基础特征
    age = np.random.normal(33, 12, n_samples).clip(17, 70)
    educ = np.random.normal(11, 4, n_samples).clip(3, 18)
    black = np.random.binomial(1, 0.4, n_samples)
    hisp = np.random.binomial(1, 0.15, n_samples)
    married = np.random.binomial(1, 0.5, n_samples)
    nodegr = np.random.binomial(1, 0.5, n_samples)

    # 生成历史收入和失业状态
    re74 = np.random.exponential(7500, n_samples) * (1 + 0.15 * educ/12) * (1 - 0.25 * black) * (1 - 0.15 * hisp)
    re75 = re74 * np.random.normal(1.08, 0.25, n_samples).clip(0.3, 2.5)

    # 失业状态（1974, 1975年）
    u74 = np.random.binomial(1, 0.3 + 0.2 * black + 0.1 * hisp - 0.1 * married, n_samples)
    u75 = np.random.binomial(1, 0.25 + 0.15 * black + 0.08 * hisp - 0.08 * married + 0.3 * u74, n_samples)

    # 生成处理变量
    propensity_score = (
        -1.8 +
        0.03 * age +
        0.08 * educ +
        0.4 * black +
        0.25 * hisp +
        -0.3 * married +
        0.6 * nodegr +
        -0.00008 * re74 +
        -0.00008 * re75 +
        0.5 * u74 +
        0.4 * u75
    )
    treat_prob = 1 / (1 + np.exp(-propensity_score))
    treat = np.random.binomial(1, treat_prob, n_samples)

    # 生成结果变量（1978年收入）
    treatment_effect = 1800 + 150 * educ/12 - 400 * black - 300 * hisp + 200 * married

    re78 = (
        1800 +  # 基础收入
        80 * age +
        600 * educ +
        -1200 * black +
        -900 * hisp +
        900 * married +
        -700 * nodegr +
        0.25 * re74 +
        0.35 * re75 +
        -800 * u74 +
        -600 * u75 +
        treat * treatment_effect +  # 处理效应
        np.random.normal(0, 2200, n_samples)  # 噪声
    ).clip(0, None)  # 收入不能为负

    # 创建DataFrame
    lalonde_df = pd.DataFrame({
        'age': age.astype(int),
        'educ': educ.round(1),
        'black': black,
        'hisp': hisp,
        'married': married,
        'nodegr': nodegr,
        're74': re74.round(2),
        're75': re75.round(2),
        'u74': u74,
        'u75': u75,
        'treat': treat,
        're78': re78.round(2)
    })

    # 保存数据
    lalonde_df.to_csv("data/lalonde/lalonde.csv", index=False)
    print(f"  ✓ LaLonde数据: {lalonde_df.shape}")
    print(f"  处理组比例: {treat.mean():.3f}")
    print(f"  平均处理效应: {lalonde_df[lalonde_df['treat']==1]['re78'].mean() - lalonde_df[lalonde_df['treat']==0]['re78'].mean():.2f}")


def verify_data_structure():
    """验证生成的数据结构是否符合配置要求"""

    print("\n验证数据结构...")

    # 验证Office-Home数据
    print("\nOffice-Home数据集:")

    # 检查训练数据
    train_path = "data/office_home/real_world_train.csv"
    if os.path.exists(train_path):
        df = pd.read_csv(train_path)
        print(f"  训练数据: {df.shape}")
        print(f"  特征列: {list(df.columns[:-1])}")
        print(f"  目标列: {df.columns[-1]}")
        print(f"  类别分布: {df['category'].value_counts().to_dict()}")

    # 检查测试数据
    test_domains = ['art', 'clipart', 'product']
    for domain in test_domains:
        test_path = f"data/office_home/{domain}_test.csv"
        if os.path.exists(test_path):
            df = pd.read_csv(test_path)
            print(f"  {domain}测试数据: {df.shape}")

    # 验证Terra Incognita数据
    print("\nTerra Incognita数据集:")

    # 检查训练数据
    train_path = "data/terra/location_100_train.csv"
    if os.path.exists(train_path):
        df = pd.read_csv(train_path)
        print(f"  训练数据: {df.shape}")
        print(f"  特征列: {list(df.columns[:-1])}")
        print(f"  目标列: {df.columns[-1]}")
        print(f"  物种分布: {df['species'].value_counts().to_dict()}")

    # 检查测试数据
    test_locations = ['location_38', 'location_43', 'location_46', 'location_47']
    for location in test_locations:
        test_path = f"data/terra/{location}_test.csv"
        if os.path.exists(test_path):
            df = pd.read_csv(test_path)
            print(f"  {location}测试数据: {df.shape}")

    # 验证因果推断数据
    print("\n因果推断数据集:")

    # 检查Jobs数据
    jobs_path = "data/jobs/jobs.csv"
    if os.path.exists(jobs_path):
        df = pd.read_csv(jobs_path)
        print(f"  Jobs数据: {df.shape}")
        print(f"  特征列: {[col for col in df.columns if col not in ['treat', 're78']]}")
        print(f"  处理列: treat")
        print(f"  目标列: re78")
        print(f"  处理组分布: {df['treat'].value_counts().to_dict()}")
        print(f"  平均收入 - 处理组: {df[df['treat']==1]['re78'].mean():.2f}, 对照组: {df[df['treat']==0]['re78'].mean():.2f}")

    # 检查LaLonde数据
    lalonde_path = "data/lalonde/lalonde.csv"
    if os.path.exists(lalonde_path):
        df = pd.read_csv(lalonde_path)
        print(f"  LaLonde数据: {df.shape}")
        print(f"  特征列: {[col for col in df.columns if col not in ['treat', 're78']]}")
        print(f"  处理列: treat")
        print(f"  目标列: re78")
        print(f"  处理组分布: {df['treat'].value_counts().to_dict()}")
        print(f"  平均收入 - 处理组: {df[df['treat']==1]['re78'].mean():.2f}, 对照组: {df[df['treat']==0]['re78'].mean():.2f}")

    # 验证Jobs数据
    print("\nJobs数据集:")

    jobs_path = "data/jobs/jobs.csv"
    if os.path.exists(jobs_path):
        df = pd.read_csv(jobs_path)
        print(f"  数据: {df.shape}")
        print(f"  特征列: {[col for col in df.columns if col not in ['treat', 're78']]}")
        print(f"  处理列: treat")
        print(f"  目标列: re78")
        print(f"  处理组分布: {df['treat'].value_counts().to_dict()}")
        print(f"  收入统计: 均值={df['re78'].mean():.2f}, 标准差={df['re78'].std():.2f}")

    # 验证LaLonde数据
    print("\nLaLonde数据集:")

    lalonde_path = "data/lalonde/lalonde.csv"
    if os.path.exists(lalonde_path):
        df = pd.read_csv(lalonde_path)
        print(f"  数据: {df.shape}")
        print(f"  特征列: {[col for col in df.columns if col not in ['treat', 're78']]}")
        print(f"  处理列: treat")
        print(f"  目标列: re78")
        print(f"  处理组分布: {df['treat'].value_counts().to_dict()}")
        print(f"  收入统计: 均值={df['re78'].mean():.2f}, 标准差={df['re78'].std():.2f}")


def main():
    """主函数"""

    print("生成OOD和因果推断数据集的模拟数据")
    print("="*60)

    # 生成Office-Home数据
    create_office_home_data()

    print()

    # 生成Terra Incognita数据
    create_terra_incognita_data()

    print()

    # 生成因果推断数据
    create_jobs_data()

    print()

    create_lalonde_data()

    # 验证数据结构
    verify_data_structure()

    print("\n" + "="*60)
    print("模拟数据生成完成！")
    print("\n生成的文件:")
    print("OOD数据集:")
    print("  Office-Home:")
    print("    - data/office_home/real_world_train.csv")
    print("    - data/office_home/art_test.csv")
    print("    - data/office_home/clipart_test.csv")
    print("    - data/office_home/product_test.csv")
    print("  Terra Incognita:")
    print("    - data/terra/location_100_train.csv")
    print("    - data/terra/location_38_test.csv")
    print("    - data/terra/location_43_test.csv")
    print("    - data/terra/location_46_test.csv")
    print("    - data/terra/location_47_test.csv")
    print("\n因果推断数据集:")
    print("  Jobs:")
    print("    - data/jobs/jobs.csv")
    print("  LaLonde:")
    print("    - data/lalonde/lalonde.csv")
    print("\n现在可以运行 real_data_evaluator.py 来验证模型评估代码！")


if __name__ == "__main__":
    main()
