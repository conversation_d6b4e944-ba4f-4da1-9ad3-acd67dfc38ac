"""
real_data_evaluator.py - 真实数据集评估框架

该文件提供了对三类真实数据集的评估功能：
1. 因果发现数据集（Causal Discovery）：有真实因果邻接矩阵，支持节点关系分析
2. 因果效应评估数据集（Causal Inference）：使用meta-learner框架评估因果效应估计能力
3. 分布外泛化数据集（OOD）：评估模型在不同分布上的泛化能力

"""

import os
import json
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import traceback
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split

# 导入现有组件
from scm_test import (
    ModelManager, FeatureImportanceCalculator, ResultCollector,
    ModelConfig
)
from utils_plot import R2Plotter, ImportancePlotter
from utils_test import calculate_pairwise_correlations_and_mi

import matplotlib
matplotlib.use('Agg')
matplotlib.rcParams['axes.unicode_minus'] = False


@dataclass
class CausalDiscoveryDatasetConfig:
    """因果发现数据集配置"""
    name: str
    data_path: str
    adj_matrix_path: str  # 邻接矩阵文件路径
    target_node: str
    feature_columns: List[str]
    
    
@dataclass  
class CausalInferenceDatasetConfig:
    """因果效应评估数据集配置"""
    name: str
    data_path: str
    target_column: str
    feature_columns: List[str]
    treatment_column: str


@dataclass
class OODDatasetConfig:
    """分布外泛化数据集配置"""
    name: str
    train_data_path: str
    test_data_paths: Dict[str, str]  # {domain_name: test_path}
    target_column: str
    feature_columns: List[str]


@dataclass
class TaskConfig:
    """任务配置类 - 简化的任务选择"""
    causal_discovery: bool = True
    causal_estimation: bool = True
    ood: bool = True


@dataclass
class RealDataEvaluationConfig:
    """真实数据集评估总配置"""
    task_config: TaskConfig = None
    model_config: ModelConfig = None
    device: str = 'cuda'
    output_dir: str = 'real_data_results'
    train_test_split_ratio: float = 0.7

    # 提供数据集
    causal_discovery_datasets: Optional[List[CausalDiscoveryDatasetConfig]] = None
    causal_inference_datasets: Optional[List[CausalInferenceDatasetConfig]] = None
    ood_datasets: Optional[List[OODDatasetConfig]] = None


class CausalDiscoveryEvaluator:
    """因果发现数据集评估器"""
    
    def __init__(self, model_config: ModelConfig, device: str = 'cpu'):
        self.model_manager = ModelManager(device)
        self.importance_calculator = FeatureImportanceCalculator()
        self.result_collector = ResultCollector()
        self.device = device
        self.model_types = self.model_manager.get_available_models(model_config)
        
    def _load_causal_graph_data(self, config: CausalDiscoveryDatasetConfig) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """加载因果发现数据集和邻接矩阵"""
        # 加载数据
        data = pd.read_csv(config.data_path)
        
        # 提取特征和目标变量
        X = data[config.feature_columns].values
        y = data[config.target_node].values
        
        # 加载邻接矩阵
        if config.adj_matrix_path.endswith('.csv'):
            adj_matrix = pd.read_csv(config.adj_matrix_path, index_col=0).values
        elif config.adj_matrix_path.endswith('.npy'):
            adj_matrix = np.load(config.adj_matrix_path)
        else:
            adj_matrix = np.loadtxt(config.adj_matrix_path)
            
        return X, y, adj_matrix
    
    def _compute_node_relationships_from_adjacency(self, adj_matrix: np.ndarray, 
                                                  target_node: str, 
                                                  feature_columns: List[str]) -> Dict:
        """
        基于邻接矩阵计算节点关系
        
        Args:
            adj_matrix: 邻接矩阵 (n x n)，adj_matrix[i,j]=1 表示从节点i到节点j有边
            target_node: 目标节点名称
            feature_columns: 所有特征列名称（不包含目标节点）
            
        Returns:
            节点关系字典，格式与get_exclusive_node_relationships一致
        """
        # 创建完整的节点列表（特征 + 目标）
        all_nodes = feature_columns + [target_node]
        node_to_idx = {node: i for i, node in enumerate(all_nodes)}
        idx_to_node = {i: node for i, node in enumerate(all_nodes)}
        
        if target_node not in node_to_idx:
            raise ValueError(f"目标节点 {target_node} 不在节点列表中")
            
        target_idx = node_to_idx[target_node]
        all_node_set = set(all_nodes)
        classified_nodes = {target_node}
        
        # 高效的节点关系分类：只分为父、子、配偶、其他

        # 1. 父节点：使用numpy索引一次性获取
        parent_indices = np.where(adj_matrix[:, target_idx] == 1)[0]
        parents = {idx_to_node[i] for i in parent_indices}
        classified_nodes.update(parents)

        # 2. 子节点：使用numpy索引一次性获取
        child_indices = np.where(adj_matrix[target_idx, :] == 1)[0]
        children = {idx_to_node[j] for j in child_indices}
        classified_nodes.update(children)

        # 3. 配偶节点：与目标节点有共同子节点的节点
        spouses = set()
        for child_idx in child_indices:
            # 获取该子节点的所有父节点
            parent_indices_of_child = np.where(adj_matrix[:, child_idx] == 1)[0]
            for parent_idx in parent_indices_of_child:
                parent_name = idx_to_node[parent_idx]
                if parent_name not in classified_nodes:
                    spouses.add(parent_name)
        classified_nodes.update(spouses)

        # 4. 其他节点：剩余的所有节点
        others = all_node_set - classified_nodes

        return {
            'parents': parents,
            'children': children,
            'spouses': spouses,
            'others': others
        }

    def _calculate_correlation_mi(self, X: np.ndarray, y: np.ndarray,
                                feature_columns: List[str], target_node: str) -> Dict:
        """计算相关系数和互信息"""
        # 合并特征和目标变量
        all_data = np.column_stack([X, y])
        all_feature_names = feature_columns + [target_node]

        # 计算相关系数和互信息
        result = calculate_pairwise_correlations_and_mi(all_data, all_feature_names)

        return {
            'correlation_matrix': result['correlation_matrix'],
            'mutual_info_matrix': result['mutual_info_matrix']
        }

    def evaluate_dataset(self, config: CausalDiscoveryDatasetConfig) -> Dict:
        """评估单个因果发现数据集"""
        print(f"评估因果发现数据集: {config.name}")

        # 1. 数据加载
        X, y, adj_matrix = self._load_causal_graph_data(config)
        print(f"  数据形状: X={X.shape}, y={y.shape}, 邻接矩阵={adj_matrix.shape}")

        # 2. 基于邻接矩阵计算节点关系
        node_relationships = self._compute_node_relationships_from_adjacency(
            adj_matrix, config.target_node, config.feature_columns
        )
        print(f"  节点关系: parents={len(node_relationships['parents'])}, "
              f"children={len(node_relationships['children'])}, "
              f"others={len(node_relationships['others'])}")

        # 3. 数据分割
        train_X, test_X, train_y, test_y = train_test_split(
            X, y, test_size=0.3, random_state=42
        )

        # 4. 模型训练和评估
        model_results = {}
        importance_results = {}

        for model_type in self.model_types:
            print(f"  训练模型: {model_type}")
            try:
                # 训练和评估模型
                result = self.model_manager.train_and_evaluate(
                    model_type, train_X, train_y, test_X, test_y
                )

                # 计算特征重要性
                if result['trainer'].needs_scaling():
                    test_X_for_importance = result['trainer'].scaler.transform(test_X)
                else:
                    test_X_for_importance = test_X

                importance = self.importance_calculator.calculate_importance(
                    result['model'], test_X_for_importance, test_y,
                    config.feature_columns, model_type
                )

                model_results[model_type] = {
                    'r2_train': result['metrics_train']['r2'],
                    'r2_test': result['metrics_test']['r2'],
                    'rmse_train': result['metrics_train']['rmse'],
                    'rmse_test': result['metrics_test']['rmse'],
                    'mape_train': result['metrics_train']['mape'],
                    'mape_test': result['metrics_test']['mape']
                }

                importance_results[model_type] = importance

                print(f"    R² Train: {result['metrics_train']['r2']:.3f}, "
                      f"Test: {result['metrics_test']['r2']:.3f}")

            except Exception as e:
                print(f"    模型 {model_type} 训练失败: {str(e)}")
                continue

        # 5. 计算相关系数和互信息
        print(f"  计算相关系数和互信息...")
        correlation_mi = self._calculate_correlation_mi(
            X, y, config.feature_columns, config.target_node
        )

        return {
            'dataset_name': config.name,
            'model_results': model_results,
            'importance_results': importance_results,
            'correlation_mi': correlation_mi,
            'node_relationships': node_relationships,
            'data_info': {
                'n_samples': X.shape[0],
                'n_features': X.shape[1],
                'target_node': config.target_node,
                'feature_columns': config.feature_columns
            }
        }

    def evaluate_datasets(self, datasets: List[CausalDiscoveryDatasetConfig]) -> List[Dict]:
        """评估多个因果发现数据集"""
        results = []
        for dataset_config in datasets:
            try:
                result = self.evaluate_dataset(dataset_config)
                results.append(result)
            except Exception as e:
                print(f"数据集 {dataset_config.name} 评估失败: {str(e)}")
                traceback.print_exc()
                continue

        return results


class CausalInferenceEvaluator:
    """因果效应评估数据集评估器"""

    def __init__(self, model_config: ModelConfig, device: str = 'cpu'):
        self.model_manager = ModelManager(device)
        self.device = device
        self.model_types = self.model_manager.get_available_models(model_config)

    def _load_causal_inference_data(self, config: CausalInferenceDatasetConfig) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """加载因果推断数据集"""
        data = pd.read_csv(config.data_path)

        X = data[config.feature_columns].values
        T = data[config.treatment_column].values
        Y = data[config.target_column].values

        return X, T, Y

    def _evaluate_s_learner(self, X: np.ndarray, T: np.ndarray, Y: np.ndarray) -> Dict:
        """S-learner评估：单一模型学习 X+T -> Y"""
        results = {}
        X_with_T = np.column_stack([X, T])  # 将treatment作为特征

        for model_name in self.model_types:
            try:
                # 训练测试分割
                train_X, test_X, train_Y, test_Y = train_test_split(
                    X_with_T, Y, test_size=0.3, random_state=42
                )

                # 训练模型
                model_result = self.model_manager.train_and_evaluate(
                    model_name, train_X, train_Y, test_X, test_Y
                )

                # 估计平均因果效应 ATE = E[Y|X,T=1] - E[Y|X,T=0]
                X_test_t1 = test_X.copy()
                X_test_t0 = test_X.copy()
                X_test_t1[:, -1] = 1  # 设置treatment=1
                X_test_t0[:, -1] = 0  # 设置treatment=0

                y_pred_t1 = model_result['trainer'].predict_with_scaling(X_test_t1)
                y_pred_t0 = model_result['trainer'].predict_with_scaling(X_test_t0)
                ate_estimate = np.mean(y_pred_t1 - y_pred_t0)

                results[f"{model_name}_s_learner"] = {
                    'r2_train': model_result['metrics_train']['r2'],
                    'r2_test': model_result['metrics_test']['r2'],
                    'rmse_train': model_result['metrics_train']['rmse'],
                    'rmse_test': model_result['metrics_test']['rmse'],
                    'ate_estimate': ate_estimate
                }

            except Exception as e:
                print(f"    S-learner {model_name} 失败: {str(e)}")
                continue

        return results

    def _evaluate_t_learner(self, X: np.ndarray, T: np.ndarray, Y: np.ndarray) -> Dict:
        """T-learner评估：分别为treatment和control组训练模型"""
        results = {}

        # 分离treatment和control组
        X_t1, Y_t1 = X[T == 1], Y[T == 1]
        X_t0, Y_t0 = X[T == 0], Y[T == 0]

        if len(X_t1) == 0 or len(X_t0) == 0:
            print("    T-learner: treatment或control组数据为空")
            return results

        for model_name in self.model_types:
            try:
                # 分别训练两个模型
                train_X_t1, test_X_t1, train_Y_t1, test_Y_t1 = train_test_split(
                    X_t1, Y_t1, test_size=0.3, random_state=42
                )
                train_X_t0, test_X_t0, train_Y_t0, test_Y_t0 = train_test_split(
                    X_t0, Y_t0, test_size=0.3, random_state=42
                )

                model_t1 = self.model_manager.train_and_evaluate(
                    model_name, train_X_t1, train_Y_t1, test_X_t1, test_Y_t1
                )
                model_t0 = self.model_manager.train_and_evaluate(
                    model_name, train_X_t0, train_Y_t0, test_X_t0, test_Y_t0
                )

                # 在整个测试集上估计个体因果效应
                test_X = np.vstack([test_X_t1, test_X_t0])
                ite_t1 = model_t1['trainer'].predict_with_scaling(test_X)
                ite_t0 = model_t0['trainer'].predict_with_scaling(test_X)
                ate_estimate = np.mean(ite_t1 - ite_t0)

                results[f"{model_name}_t_learner"] = {
                    'r2_t1': model_t1['metrics_test']['r2'],
                    'r2_t0': model_t0['metrics_test']['r2'],
                    'rmse_t1': model_t1['metrics_test']['rmse'],
                    'rmse_t0': model_t0['metrics_test']['rmse'],
                    'ate_estimate': ate_estimate
                }

            except Exception as e:
                print(f"    T-learner {model_name} 失败: {str(e)}")
                continue

        return results

    def evaluate_dataset(self, config: CausalInferenceDatasetConfig) -> Dict:
        """评估单个因果推断数据集"""
        print(f"评估因果推断数据集: {config.name}")

        # 1. 数据加载
        X, T, Y = self._load_causal_inference_data(config)
        print(f"  数据形状: X={X.shape}, T={T.shape}, Y={Y.shape}")
        print(f"  Treatment分布: T=1: {np.sum(T==1)}, T=0: {np.sum(T==0)}")

        # 2. Meta-learner框架评估
        results = {}

        # S-learner评估
        print("  执行S-learner评估...")
        s_results = self._evaluate_s_learner(X, T, Y)
        results.update(s_results)

        # T-learner评估
        print("  执行T-learner评估...")
        t_results = self._evaluate_t_learner(X, T, Y)
        results.update(t_results)

        return {
            'dataset_name': config.name,
            'model_results': results,
            'data_info': {
                'n_samples': X.shape[0],
                'n_features': X.shape[1],
                'n_treated': np.sum(T == 1),
                'n_control': np.sum(T == 0),
                'treatment_column': config.treatment_column,
                'target_column': config.target_column
            }
        }

    def evaluate_datasets(self, datasets: List[CausalInferenceDatasetConfig]) -> List[Dict]:
        """评估多个因果推断数据集"""
        results = []
        for dataset_config in datasets:
            try:
                result = self.evaluate_dataset(dataset_config)
                results.append(result)
            except Exception as e:
                print(f"数据集 {dataset_config.name} 评估失败: {str(e)}")
                traceback.print_exc()
                continue

        return results


class OODEvaluatorBase:
    """分布外泛化数据集评估器基类"""

    def __init__(self, model_config: ModelConfig, device: str = 'cpu'):
        self.model_manager = ModelManager(device)
        self.device = device
        self.model_types = self.model_manager.get_available_models(model_config)

    def _load_ood_data(self, config: OODDatasetConfig) -> Tuple[np.ndarray, np.ndarray, Dict[str, Tuple[np.ndarray, np.ndarray]]]:
        """加载OOD数据集"""
        # 加载训练数据（源域）
        train_data = pd.read_csv(config.train_data_path)
        train_X = train_data[config.feature_columns].values
        train_y = train_data[config.target_column].values

        # 加载测试数据（目标域）
        test_domains = {}
        for domain_name, test_path in config.test_data_paths.items():
            test_data = pd.read_csv(test_path)
            test_X = test_data[config.feature_columns].values
            test_y = test_data[config.target_column].values
            test_domains[domain_name] = (test_X, test_y)

        return train_X, train_y, test_domains

    def evaluate_dataset(self, config: OODDatasetConfig) -> Dict:
        """评估单个OOD数据集"""
        print(f"评估OOD数据集: {config.name}")

        # 1. 数据加载
        train_X, train_y, test_domains = self._load_ood_data(config)
        print(f"  训练数据形状: X={train_X.shape}, y={train_y.shape}")
        print(f"  测试域数量: {len(test_domains)}")

        results = {}

        # 2. 在每个目标域上评估
        for domain_name, (test_X, test_y) in test_domains.items():
            print(f"  评估目标域: {domain_name} (形状: {test_X.shape})")

            domain_results = {}

            # 3. 训练模型并在目标域上测试
            for model_name in self.model_types:
                try:
                    # 在源域上训练，在目标域上测试
                    model_result = self.model_manager.train_and_evaluate(
                        model_name, train_X, train_y, test_X, test_y
                    )

                    domain_results[model_name] = {
                        'r2_source': model_result['metrics_train']['r2'],  # 源域性能
                        'r2_target': model_result['metrics_test']['r2'],   # 目标域性能
                        'rmse_source': model_result['metrics_train']['rmse'],
                        'rmse_target': model_result['metrics_test']['rmse']
                    }
                    print(f"{model_name}: R² target={model_result['metrics_test']['r2']:.3f}\n"
                        f"RMSE target={model_result['metrics_test']['rmse']:.3f}")

                except Exception as e:
                    print(f"    模型 {model_name} 在域 {domain_name} 上失败: {str(e)}")
                    continue

            results[domain_name] = domain_results

        return {
            'dataset_name': config.name,
            'domain_results': results,
            'data_info': {
                'n_train_samples': train_X.shape[0],
                'n_features': train_X.shape[1],
                'n_test_domains': len(test_domains),
                'test_domain_names': list(test_domains.keys()),
                'target_column': config.target_column
            }
        }

    def evaluate_datasets(self, datasets: List[OODDatasetConfig]) -> List[Dict]:
        """评估多个OOD数据集"""
        results = []
        for dataset_config in datasets:
            try:
                result = self.evaluate_dataset(dataset_config)
                results.append(result)
            except Exception as e:
                print(f"数据集 {dataset_config.name} 评估失败: {str(e)}")
                traceback.print_exc()
                continue

        return results





class RealDataVisualizer:
    """真实数据集可视化器"""

    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.r2_plotter = R2Plotter()
        self.importance_plotter = ImportancePlotter()

    def generate_causal_discovery_visualizations(self, results: List[Dict]):
        """为因果发现数据集生成完整可视化"""
        print("生成因果发现数据集可视化...")

        # 1. 准备R²数据
        r2_data = []
        for result in results:
            dataset_name = result['dataset_name']
            for model_type, metrics in result['model_results'].items():
                r2_data.append({
                    'dataset': dataset_name,
                    'model_type': model_type,
                    'r2_train': metrics['r2_train'],
                    'r2_test': metrics['r2_test'],
                    'rmse_train': metrics['rmse_train'],
                    'rmse_test': metrics['rmse_test'],
                    'mape_train': metrics['mape_train'],
                    'mape_test': metrics['mape_test']
                })

        # 2. 生成R²对比箱线图
        if r2_data:
            self.r2_plotter.plot_r2_comparison(
                r2_data, self.output_dir, r2_threshold=0.5,
                annotate_all_datasets=True, annotate_effective_datasets=False
            )

        # 3. 准备特征重要性数据
        importance_data = []
        for result in results:
            dataset_name = result['dataset_name']
            node_relationships = result['node_relationships']

            for model_type, importance_dict in result['importance_results'].items():
                if 'permutation' in importance_dict:
                    for feature_name, importance_value in importance_dict['permutation'].items():
                        # 确定节点类型
                        node_type = 'others'  # 默认
                        for rel_type, nodes in node_relationships.items():
                            if feature_name in nodes:
                                node_type = rel_type
                                break

                        importance_data.append({
                            'dataset': dataset_name,
                            'model_type': model_type,
                            'feature_name': feature_name,
                            'importance': importance_value,
                            'node_type': node_type
                        })

        # 4. 生成特征重要性箱线图
        if importance_data:
            # 创建模拟的scm_objects用于兼容现有接口
            mock_scm_objects = {}
            for i, result in enumerate(results):
                mock_scm_objects[i] = {
                    'config_key': result['dataset_name'],
                    'node_relationships': result['node_relationships']
                }

            self.importance_plotter.plot_permutation_importance_comparison(
                importance_data, self.output_dir,
                scm_objects=mock_scm_objects,
                custom_functions_configs={},
                results=r2_data,
                r2_threshold=0.5,
                annotate_all_datasets=True,
                annotate_effective_datasets=False,
                effective_datasets={}
            )

        # 5. 保存详细JSON结果
        self._save_detailed_json(results, 'causal_discovery_results.json')

        # 6. 保存CSV汇总
        self._save_csv_summary(r2_data, 'causal_discovery_summary.csv')

    def generate_simple_visualizations(self, results: List[Dict], dataset_type: str):
        """为简单数据集生成基础可视化"""
        print(f"生成{dataset_type}数据集可视化...")

        # 准备数据
        plot_data = []
        if dataset_type == 'causal_inference':
            for result in results:
                dataset_name = result['dataset_name']
                for model_type, metrics in result['model_results'].items():
                    # 提取主要指标
                    if 's_learner' in model_type:
                        plot_data.append({
                            'dataset': dataset_name,
                            'model_type': model_type,
                            'r2_train': metrics.get('r2_train', 0),
                            'r2_test': metrics.get('r2_test', 0),
                            'ate_estimate': metrics.get('ate_estimate', 0)
                        })
                    elif 't_learner' in model_type:
                        plot_data.append({
                            'dataset': dataset_name,
                            'model_type': model_type,
                            'r2_train': (metrics.get('r2_t1', 0) + metrics.get('r2_t0', 0)) / 2,  # 平均R²
                            'r2_test': (metrics.get('r2_t1', 0) + metrics.get('r2_t0', 0)) / 2,   # 平均R²
                            'r2_t1': metrics.get('r2_t1', 0),
                            'r2_t0': metrics.get('r2_t0', 0),
                            'ate_estimate': metrics.get('ate_estimate', 0)
                        })

            # 生成R²对比箱线图
            if plot_data:
                print(f"  生成{dataset_type}数据集R²箱线图...")
                self.r2_plotter.plot_r2_comparison(
                    plot_data, self.output_dir, r2_threshold=0.0,
                    annotate_all_datasets=True, annotate_effective_datasets=False,
                    filename_prefix=f'{dataset_type}_model_comparison'
                )

        elif dataset_type == 'ood':
            # 重新组织数据结构，确保r2列挨着，rmse列挨着
            for result in results:
                dataset_name = result['dataset_name']
                for domain_name, domain_results in result['domain_results'].items():
                    for model_type, metrics in domain_results.items():
                        # 创建有序的字典，确保列的顺序
                        row_data = {
                            'dataset': f"{dataset_name}_{domain_name}",
                            'model_type': model_type
                        }

                        # 先添加所有r2相关列
                        row_data['r2_source'] = metrics['r2_source']
                        row_data['r2_target'] = metrics['r2_target']

                        # 再添加所有rmse相关列
                        row_data['rmse_source'] = metrics['rmse_source']
                        row_data['rmse_target'] = metrics['rmse_target']

                        plot_data.append(row_data)

        # 保存CSV汇总
        self._save_csv_summary(plot_data, f'{dataset_type}_summary.csv')


    def _save_detailed_json(self, results: List[Dict], filename: str):
        """保存详细JSON结果"""
        json_path = os.path.join(self.output_dir, filename)
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        print(f"  保存JSON文件: {filename}")

    def _save_csv_summary(self, data: List[Dict], filename: str):
        """保存CSV汇总"""
        if not data:
            return

        df = pd.DataFrame(data)

        # 对于OOD数据，重新排序列，确保r2列挨着，rmse列挨着
        if 'ood' in filename:
            # 获取所有列名
            columns = list(df.columns)

            # 基础列
            base_columns = ['dataset', 'model_type']

            # r2相关列
            r2_columns = [col for col in columns if col.startswith('r2_')]
            r2_columns.sort()  # 按字母顺序排序

            # rmse相关列
            rmse_columns = [col for col in columns if col.startswith('rmse_')]
            rmse_columns.sort()  # 按字母顺序排序

            # 其他列
            other_columns = [col for col in columns if col not in base_columns + r2_columns + rmse_columns]

            # 重新排序列
            ordered_columns = base_columns + r2_columns + rmse_columns + other_columns
            df = df[ordered_columns]

        csv_path = os.path.join(self.output_dir, filename)
        df.to_csv(csv_path, index=False)
        print(f"  保存CSV文件: {filename}")


def evaluate_real_datasets(config: RealDataEvaluationConfig):
    """统一的真实数据集评估入口函数"""

    print("="*60)
    print("真实数据集评估开始")
    print("="*60)

    # 根据TaskConfig确定要执行的任务
    task_config = config.task_config
    if task_config is None:
        # 如果没有提供TaskConfig，根据数据集配置推断
        task_config = TaskConfig(
            causal_discovery=config.causal_discovery_datasets is not None,
            causal_estimation=config.causal_inference_datasets is not None,
            ood=config.ood_datasets is not None
        )

    # 准备数据集 - 必须由用户提供
    causal_discovery_datasets = config.causal_discovery_datasets
    causal_inference_datasets = config.causal_inference_datasets
    ood_datasets = config.ood_datasets

    # 检查任务配置与数据集配置的一致性
    if task_config.causal_discovery and causal_discovery_datasets is None:
        print("警告: 启用了因果发现评估但未提供数据集，将跳过此任务")
        task_config.causal_discovery = False

    if task_config.causal_estimation and causal_inference_datasets is None:
        print("警告: 启用了因果效应评估但未提供数据集，将跳过此任务")
        task_config.causal_estimation = False

    if task_config.ood and ood_datasets is None:
        print("警告: 启用了OOD评估但未提供数据集，将跳过此任务")
        task_config.ood = False

    # 生成输出目录名 - 只包含实际执行的任务
    task_names = []
    if task_config.causal_discovery and causal_discovery_datasets:
        task_names.append("discovery")
    if task_config.causal_estimation and causal_inference_datasets:
        task_names.append("estimation")
    if task_config.ood and ood_datasets:
        task_names.append("ood")

    if not task_names:
        print("错误: 没有可执行的任务，请检查任务配置和数据集配置")
        return {}

    run_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(config.output_dir, f"{'_'.join(task_names)}_{run_time}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    results = {}

    # 1. 因果发现数据集评估
    if task_config.causal_discovery and causal_discovery_datasets:
        print("\n" + "="*40)
        print("评估因果发现数据集")
        print("="*40)

        cd_evaluator = CausalDiscoveryEvaluator(config.model_config, config.device)
        cd_results = cd_evaluator.evaluate_datasets(causal_discovery_datasets)

        if cd_results:
            # 生成可视化和保存结果
            visualizer = RealDataVisualizer(output_dir)
            visualizer.generate_causal_discovery_visualizations(cd_results)
            results['causal_discovery'] = cd_results
            print(f"因果发现数据集评估完成，共处理 {len(cd_results)} 个数据集")
        else:
            print("因果发现数据集评估失败，无有效结果")

    # 2. 因果效应评估数据集
    if task_config.causal_estimation and causal_inference_datasets:
        print("\n" + "="*40)
        print("评估因果效应估计数据集")
        print("="*40)

        ci_evaluator = CausalInferenceEvaluator(config.model_config, config.device)
        ci_results = ci_evaluator.evaluate_datasets(causal_inference_datasets)

        if ci_results:
            # 生成可视化和保存结果
            visualizer = RealDataVisualizer(output_dir)
            visualizer.generate_simple_visualizations(ci_results, 'causal_inference')
            results['causal_inference'] = ci_results
            print(f"因果效应评估数据集评估完成，共处理 {len(ci_results)} 个数据集")
        else:
            print("因果效应评估数据集评估失败，无有效结果")

    # 3. OOD数据集评估
    if task_config.ood and ood_datasets:
        print("\n" + "="*40)
        print("评估分布外泛化数据集")
        print("="*40)

        ood_evaluator = OODEvaluator(config.model_config, config.device)
        ood_results = ood_evaluator.evaluate_datasets(ood_datasets)

        if ood_results:
            # 生成可视化和保存结果
            visualizer = RealDataVisualizer(output_dir)
            visualizer.generate_simple_visualizations(ood_results, 'ood')
            results['ood'] = ood_results
            print(f"OOD数据集评估完成，共处理 {len(ood_results)} 个数据集")
        else:
            print("OOD数据集评估失败，无有效结果")

    print("\n" + "="*60)
    print("真实数据集评估完成")
    print(f"结果保存在: {output_dir}")
    print("="*60)

    return results


def create_sample_causal_discovery_configs():
    """
    创建因果发现数据集配置示例

    注意：这些是真实数据集的配置示例，需要下载相应的数据文件。
    如果数据文件不存在，评估将跳过这些数据集。

    数据集下载说明：
    - Sachs: https://www.bnlearn.com/bnrepository/discrete-small.html#sachs
    - Child: https://www.bnlearn.com/bnrepository/discrete-medium.html#child
    - Alarm: https://www.bnlearn.com/bnrepository/discrete-large.html#alarm
    - Asia: https://www.bnlearn.com/bnrepository/discrete-small.html#asia
    """
    return [
        # Sachs数据集 - 蛋白质信号网络
        CausalDiscoveryDatasetConfig(
            name="sachs",
            data_path="data/sachs/sachs_data.csv",
            adj_matrix_path="data/sachs/sachs_adj.csv",
            target_node="Akt",
            feature_columns=["PKC", "Mek", "Raf", "Jnk", "P38", "PIP2", "PIP3", "Plcg"]
        ),
        # Child数据集 - 儿童疾病诊断
        CausalDiscoveryDatasetConfig(
            name="child",
            data_path="data/child/child_data.csv",
            adj_matrix_path="data/child/child_adj.csv",
            target_node="Disease",
            feature_columns=["Age", "Sex", "LungParench", "CO2", "ChestXray", "Grunting", "LVHreport", "LowerBodyO2", "RUQO2", "CO2Report", "XrayReport", "GruntingReport"]
        ),
        # Alarm数据集 - 医疗监控网络
        CausalDiscoveryDatasetConfig(
            name="alarm",
            data_path="data/alarm/alarm_data.csv",
            adj_matrix_path="data/alarm/alarm_adj.csv",
            target_node="HYPOVOLEMIA",
            feature_columns=["CVP", "PCWP", "HISTORY", "TPR", "BP", "CO", "HRBP", "HREK", "HRSAT", "ANAPHYLAXIS", "INSUFFANESTH", "PULMEMBOLUS", "KINKEDTUBE", "DISCONNECT", "MINVOLSET", "VENTMACH", "VENTTUBE", "VENTLUNG", "VENTALV", "ARTCO2", "CATECHOL", "HR", "ERRLOWOUTPUT", "HREKG", "ERRCAUTER", "HRSAT", "EXPCO2", "MINVOL", "FIO2", "PVSAT", "SAO2", "SHUNT"]
        ),
        # Asia数据集 - 肺癌诊断网络
        CausalDiscoveryDatasetConfig(
            name="asia",
            data_path="data/asia/asia_data.csv",
            adj_matrix_path="data/asia/asia_adj.csv",
            target_node="lung",
            feature_columns=["asia", "tub", "smoke", "bronc", "either", "xray", "dysp"]
        ),
        # Cancer数据集 - 癌症风险评估
        CausalDiscoveryDatasetConfig(
            name="cancer",
            data_path="data/cancer/cancer_data.csv",
            adj_matrix_path="data/cancer/cancer_adj.csv",
            target_node="Cancer",
            feature_columns=["Pollution", "Smoker", "Xray", "Dyspnoea"]
        ),
        # Earthquake数据集 - 地震预测网络
        CausalDiscoveryDatasetConfig(
            name="earthquake",
            data_path="data/earthquake/earthquake_data.csv",
            adj_matrix_path="data/earthquake/earthquake_adj.csv",
            target_node="Earthquake",
            feature_columns=["Burglary", "Alarm", "JohnCalls", "MaryCalls"]
        )
    ]


def create_sample_causal_inference_configs():
    """创建因果推断数据集配置示例"""
    return [
        # IHDP数据集 - 婴儿健康发展项目
        # CausalInferenceDatasetConfig(
        #     name="ihdp",
        #     data_path="data/ihdp/ihdp.csv",
        #     target_column="y_factual",
        #     feature_columns=["x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10", "x11", "x12", "x13", "x14", "x15", "x16", "x17", "x18", "x19", "x20", "x21", "x22", "x23", "x24", "x25"],
        #     treatment_column="treatment"
        # ),
        # Jobs数据集 - 就业培训项目
        CausalInferenceDatasetConfig(
            name="jobs",
            data_path="data/jobs/jobs.csv",
            target_column="re78",
            feature_columns=["age", "education", "black", "hispanic", "married", "nodegree", "re74", "re75"],
            treatment_column="treat"
        ),
        # Twins数据集 - 双胞胎研究
        # CausalInferenceDatasetConfig(
        #     name="twins",
        #     data_path="data/twins/twins.csv",
        #     target_column="y",
        #     feature_columns=["gestat10", "birwt", "precare", "momage", "nprevistg", "dfageq", "feduc6", "infant", "omaps", "fmaps"],
        #     treatment_column="t"
        # ),
        # ACIC 2016数据集 - 因果推断竞赛数据
        # CausalInferenceDatasetConfig(
        #     name="acic_2016",
        #     data_path="data/acic/acic_2016.csv",
        #     target_column="Y",
        #     feature_columns=["X1", "X2", "X3", "X4", "X5", "X6", "X7", "X8", "X9", "X10", "X11", "X12", "X13", "X14", "X15", "X16", "X17", "X18", "X19", "X20"],
        #     treatment_column="Z"
        # ),
        # LaLonde数据集 - 劳动经济学研究
        CausalInferenceDatasetConfig(
            name="lalonde",
            data_path="data/lalonde/lalonde.csv",
            target_column="re78",
            feature_columns=["age", "educ", "black", "hisp", "married", "nodegr", "re74", "re75", "u74", "u75"],
            treatment_column="treat"
        )
    ]


def create_sample_ood_configs():
    """创建OOD数据集配置示例"""
    return [       
        # Office-Home数据集 - 办公用品跨域
        OODDatasetConfig(
            name="office_home",
            train_data_path="data/office_home/real_world_train.csv",
            test_data_paths={
                "art": "data/office_home/art_test.csv",
                "clipart": "data/office_home/clipart_test.csv",
                "product": "data/office_home/product_test.csv"
            },
            target_column="category",
            feature_columns=["feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7", "feature_8", "feature_9", "feature_10"]
        ),
        # Terra Incognita数据集 - 地理位置泛化
        OODDatasetConfig(
            name="terra_incognita",
            train_data_path="data/terra/location_100_train.csv",
            test_data_paths={
                "location_38": "data/terra/location_38_test.csv",
                "location_43": "data/terra/location_43_test.csv",
                "location_46": "data/terra/location_46_test.csv",
                "location_47": "data/terra/location_47_test.csv"
            },
            target_column="species",
            feature_columns=["env_1", "env_2", "env_3", "env_4", "env_5", "env_6", "env_7", "env_8", "env_9", "env_10", "env_11", "env_12", "env_13", "env_14", "env_15"]
        )
        
    ]


def main():
    """真实数据集评估"""

    print("真实数据集评估系统")
    print("="*50)

    # 配置模型
    model_config = ModelConfig(
        ols=True,
        lasso=False,
        catboost=False,
        xgboost=True,
        lightgbm=True,
        tabpfn_default=False,
        tabpfn_mse=False,
        tabpfn_muzero=False
    )

    # 配置任务
    task_config = TaskConfig(
        causal_discovery=False,
        causal_estimation=False,
        ood=True
    )

    # 根据任务配置创建相应的数据集
    causal_discovery_datasets = None
    causal_inference_datasets = None
    ood_datasets = None

    if task_config.causal_discovery:
        causal_discovery_datasets = create_sample_causal_discovery_configs()
        print("✓ 配置因果发现数据集")

    if task_config.causal_estimation:
        causal_inference_datasets = create_sample_causal_inference_configs()
        print("✓ 配置因果推断数据集")

    if task_config.ood:
        ood_datasets = create_sample_ood_configs()
        print("✓ 配置OOD数据集")

    # 创建评估配置
    config = RealDataEvaluationConfig(
        task_config=task_config,
        model_config=model_config,
        causal_discovery_datasets=causal_discovery_datasets,
        causal_inference_datasets=causal_inference_datasets,
        ood_datasets=ood_datasets,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='real_data_results'
    )

    print(f"\n使用设备: {config.device}")
    print("开始评估...")

    # 执行评估
    results = evaluate_real_datasets(config)

    # 输出结果概览
    print("\n" + "="*50)
    print("评估结果概览")
    print("="*50)

    total_datasets = 0
    for dataset_type, type_results in results.items():
        print(f"\n{dataset_type.upper()}:")
        print(f"  成功评估: {len(type_results)} 个数据集")
        total_datasets += len(type_results)

        for result in type_results:
            dataset_name = result['dataset_name']
            if dataset_type == 'causal_discovery':
                n_models = len(result['model_results'])
                print(f"    {dataset_name}: {n_models} 个模型")
            elif dataset_type == 'causal_inference':
                n_methods = len(result['model_results'])
                print(f"    {dataset_name}: {n_methods} 个meta-learner方法")
            elif dataset_type == 'ood':
                n_domains = len(result['domain_results'])
                print(f"    {dataset_name}: {n_domains} 个目标域")

    print(f"\n总计成功评估: {total_datasets} 个数据集")
    print("评估完成！结果已保存到相应的输出目录。")

    return results


if __name__ == "__main__":
    main()
