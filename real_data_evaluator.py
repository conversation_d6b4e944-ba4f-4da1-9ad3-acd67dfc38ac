"""
real_data_evaluator.py - 真实数据集评估框架

该文件提供了对三类真实数据集的评估功能：
1. 因果发现数据集（Causal Discovery）：有真实因果邻接矩阵，支持节点关系分析
2. 因果效应评估数据集（Causal Inference）：使用meta-learner框架评估因果效应估计能力
3. 分布外泛化数据集（OOD）：评估模型在不同分布上的泛化能力

"""

import os
import json
import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import traceback
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import train_test_split

# 导入现有组件
from scm_test import (
    ModelManager, FeatureImportanceCalculator, ResultCollector,
    ModelConfig
)
from utils_plot import R2Plotter, ImportancePlotter
from utils_test import calculate_pairwise_correlations_and_mi

import matplotlib
matplotlib.use('Agg')
matplotlib.rcParams['axes.unicode_minus'] = False


@dataclass
class CausalDiscoveryDatasetConfig:
    """因果发现数据集配置"""
    name: str
    data_path: str
    adj_matrix_path: str  # 邻接矩阵文件路径
    target_node: str
    feature_columns: List[str]
    
    
@dataclass  
class CausalInferenceDatasetConfig:
    """因果效应评估数据集配置"""
    name: str
    data_path: str
    target_column: str
    feature_columns: List[str]
    treatment_column: str


@dataclass
class OODDatasetConfig:
    """分布外泛化数据集配置"""
    name: str
    train_data_path: str
    test_data_paths: Dict[str, str]  # {domain_name: test_path}
    target_column: str
    feature_columns: List[str]


@dataclass
class RealDataEvaluationConfig:
    """真实数据集评估总配置"""
    causal_discovery_datasets: Optional[List[CausalDiscoveryDatasetConfig]] = None
    causal_inference_datasets: Optional[List[CausalInferenceDatasetConfig]] = None
    ood_datasets: Optional[List[OODDatasetConfig]] = None
    model_config: ModelConfig = None
    device: str = 'cuda'
    num_workers: int = 8
    output_dir: str = 'real_data_results'
    train_test_split_ratio: float = 0.7


class CausalDiscoveryEvaluator:
    """因果发现数据集评估器"""
    
    def __init__(self, model_config: ModelConfig, device: str = 'cpu'):
        self.model_manager = ModelManager(device)
        self.importance_calculator = FeatureImportanceCalculator()
        self.result_collector = ResultCollector()
        self.device = device
        self.model_types = self.model_manager.get_available_models(model_config)
        
    def _load_causal_graph_data(self, config: CausalDiscoveryDatasetConfig) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """加载因果发现数据集和邻接矩阵"""
        # 加载数据
        data = pd.read_csv(config.data_path)
        
        # 提取特征和目标变量
        X = data[config.feature_columns].values
        y = data[config.target_node].values
        
        # 加载邻接矩阵
        if config.adj_matrix_path.endswith('.csv'):
            adj_matrix = pd.read_csv(config.adj_matrix_path, index_col=0).values
        elif config.adj_matrix_path.endswith('.npy'):
            adj_matrix = np.load(config.adj_matrix_path)
        else:
            adj_matrix = np.loadtxt(config.adj_matrix_path)
            
        return X, y, adj_matrix
    
    def _compute_node_relationships_from_adjacency(self, adj_matrix: np.ndarray, 
                                                  target_node: str, 
                                                  feature_columns: List[str]) -> Dict:
        """
        基于邻接矩阵计算节点关系
        
        Args:
            adj_matrix: 邻接矩阵 (n x n)，adj_matrix[i,j]=1 表示从节点i到节点j有边
            target_node: 目标节点名称
            feature_columns: 所有特征列名称（不包含目标节点）
            
        Returns:
            节点关系字典，格式与get_exclusive_node_relationships一致
        """
        # 创建完整的节点列表（特征 + 目标）
        all_nodes = feature_columns + [target_node]
        node_to_idx = {node: i for i, node in enumerate(all_nodes)}
        idx_to_node = {i: node for i, node in enumerate(all_nodes)}
        
        if target_node not in node_to_idx:
            raise ValueError(f"目标节点 {target_node} 不在节点列表中")
            
        target_idx = node_to_idx[target_node]
        all_node_set = set(all_nodes)
        classified_nodes = {target_node}
        
        # 1. 父节点：指向目标节点的节点
        parents = set()
        for i in range(len(all_nodes)):
            if adj_matrix[i, target_idx] == 1:
                parents.add(idx_to_node[i])
        classified_nodes.update(parents)
        
        # 2. 子节点：目标节点指向的节点  
        children = set()
        for j in range(len(all_nodes)):
            if adj_matrix[target_idx, j] == 1:
                children.add(idx_to_node[j])
        classified_nodes.update(children)
        
        # 3. 配偶节点：与目标节点有共同子节点的节点
        spouses = set()
        for child_name in children:
            child_idx = node_to_idx[child_name]
            for i in range(len(all_nodes)):
                if adj_matrix[i, child_idx] == 1:
                    parent_name = idx_to_node[i]
                    if parent_name not in classified_nodes:
                        spouses.add(parent_name)
        classified_nodes.update(spouses)
        
        # 4. 祖父节点：父节点的父节点
        grandparents = set()
        for parent_name in parents:
            parent_idx = node_to_idx[parent_name]
            for i in range(len(all_nodes)):
                if adj_matrix[i, parent_idx] == 1:
                    grandparent_name = idx_to_node[i]
                    if grandparent_name not in classified_nodes:
                        grandparents.add(grandparent_name)
        classified_nodes.update(grandparents)
        
        # 5. 兄弟节点：与目标节点有共同父节点的节点
        siblings = set()
        for parent_name in parents:
            parent_idx = node_to_idx[parent_name]
            for j in range(len(all_nodes)):
                if adj_matrix[parent_idx, j] == 1:
                    sibling_name = idx_to_node[j]
                    if sibling_name not in classified_nodes:
                        siblings.add(sibling_name)
        classified_nodes.update(siblings)
        
        # 6. 其他节点：剩余的所有节点
        others = all_node_set - classified_nodes
        
        return {
            'parents': parents,
            'children': children,
            'spouses': spouses,
            'grandparents': grandparents,
            'siblings': siblings,
            'others': others
        }

    def _calculate_correlation_mi(self, X: np.ndarray, y: np.ndarray,
                                feature_columns: List[str], target_node: str) -> Dict:
        """计算相关系数和互信息"""
        # 合并特征和目标变量
        all_data = np.column_stack([X, y])
        all_feature_names = feature_columns + [target_node]

        # 计算相关系数和互信息
        result = calculate_pairwise_correlations_and_mi(all_data, all_feature_names)

        return {
            'correlation_matrix': result['correlation_matrix'],
            'mutual_info_matrix': result['mutual_info_matrix']
        }

    def evaluate_dataset(self, config: CausalDiscoveryDatasetConfig) -> Dict:
        """评估单个因果发现数据集"""
        print(f"评估因果发现数据集: {config.name}")

        # 1. 数据加载
        X, y, adj_matrix = self._load_causal_graph_data(config)
        print(f"  数据形状: X={X.shape}, y={y.shape}, 邻接矩阵={adj_matrix.shape}")

        # 2. 基于邻接矩阵计算节点关系
        node_relationships = self._compute_node_relationships_from_adjacency(
            adj_matrix, config.target_node, config.feature_columns
        )
        print(f"  节点关系: parents={len(node_relationships['parents'])}, "
              f"children={len(node_relationships['children'])}, "
              f"others={len(node_relationships['others'])}")

        # 3. 数据分割
        train_X, test_X, train_y, test_y = train_test_split(
            X, y, test_size=0.3, random_state=42
        )

        # 4. 模型训练和评估
        model_results = {}
        importance_results = {}

        for model_type in self.model_types:
            print(f"  训练模型: {model_type}")
            try:
                # 训练和评估模型
                result = self.model_manager.train_and_evaluate(
                    model_type, train_X, train_y, test_X, test_y
                )

                # 计算特征重要性
                if result['trainer'].needs_scaling():
                    test_X_for_importance = result['trainer'].scaler.transform(test_X)
                else:
                    test_X_for_importance = test_X

                importance = self.importance_calculator.calculate_importance(
                    result['model'], test_X_for_importance, test_y,
                    config.feature_columns, model_type
                )

                model_results[model_type] = {
                    'r2_train': result['metrics_train']['r2'],
                    'r2_test': result['metrics_test']['r2'],
                    'rmse_train': result['metrics_train']['rmse'],
                    'rmse_test': result['metrics_test']['rmse'],
                    'mape_train': result['metrics_train']['mape'],
                    'mape_test': result['metrics_test']['mape']
                }

                importance_results[model_type] = importance

                print(f"    R² Train: {result['metrics_train']['r2']:.3f}, "
                      f"Test: {result['metrics_test']['r2']:.3f}")

            except Exception as e:
                print(f"    模型 {model_type} 训练失败: {str(e)}")
                continue

        # 5. 计算相关系数和互信息
        print(f"  计算相关系数和互信息...")
        correlation_mi = self._calculate_correlation_mi(
            X, y, config.feature_columns, config.target_node
        )

        return {
            'dataset_name': config.name,
            'model_results': model_results,
            'importance_results': importance_results,
            'correlation_mi': correlation_mi,
            'node_relationships': node_relationships,
            'data_info': {
                'n_samples': X.shape[0],
                'n_features': X.shape[1],
                'target_node': config.target_node,
                'feature_columns': config.feature_columns
            }
        }

    def evaluate_datasets(self, datasets: List[CausalDiscoveryDatasetConfig]) -> List[Dict]:
        """评估多个因果发现数据集"""
        results = []
        for dataset_config in datasets:
            try:
                result = self.evaluate_dataset(dataset_config)
                results.append(result)
            except Exception as e:
                print(f"数据集 {dataset_config.name} 评估失败: {str(e)}")
                traceback.print_exc()
                continue

        return results


class CausalInferenceEvaluator:
    """因果效应评估数据集评估器"""

    def __init__(self, model_config: ModelConfig, device: str = 'cpu'):
        self.model_manager = ModelManager(device)
        self.device = device
        self.model_types = self.model_manager.get_available_models(model_config)

    def _load_causal_inference_data(self, config: CausalInferenceDatasetConfig) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """加载因果推断数据集"""
        data = pd.read_csv(config.data_path)

        X = data[config.feature_columns].values
        T = data[config.treatment_column].values
        Y = data[config.target_column].values

        return X, T, Y

    def _evaluate_s_learner(self, X: np.ndarray, T: np.ndarray, Y: np.ndarray) -> Dict:
        """S-learner评估：单一模型学习 X+T -> Y"""
        results = {}
        X_with_T = np.column_stack([X, T])  # 将treatment作为特征

        for model_name in self.model_types:
            try:
                # 训练测试分割
                train_X, test_X, train_Y, test_Y = train_test_split(
                    X_with_T, Y, test_size=0.3, random_state=42
                )

                # 训练模型
                model_result = self.model_manager.train_and_evaluate(
                    model_name, train_X, train_Y, test_X, test_Y
                )

                # 估计平均因果效应 ATE = E[Y|X,T=1] - E[Y|X,T=0]
                X_test_t1 = test_X.copy()
                X_test_t0 = test_X.copy()
                X_test_t1[:, -1] = 1  # 设置treatment=1
                X_test_t0[:, -1] = 0  # 设置treatment=0

                y_pred_t1 = model_result['trainer'].predict_with_scaling(X_test_t1)
                y_pred_t0 = model_result['trainer'].predict_with_scaling(X_test_t0)
                ate_estimate = np.mean(y_pred_t1 - y_pred_t0)

                results[f"{model_name}_s_learner"] = {
                    'r2_train': model_result['metrics_train']['r2'],
                    'r2_test': model_result['metrics_test']['r2'],
                    'rmse_train': model_result['metrics_train']['rmse'],
                    'rmse_test': model_result['metrics_test']['rmse'],
                    'ate_estimate': ate_estimate
                }

            except Exception as e:
                print(f"    S-learner {model_name} 失败: {str(e)}")
                continue

        return results

    def _evaluate_t_learner(self, X: np.ndarray, T: np.ndarray, Y: np.ndarray) -> Dict:
        """T-learner评估：分别为treatment和control组训练模型"""
        results = {}

        # 分离treatment和control组
        X_t1, Y_t1 = X[T == 1], Y[T == 1]
        X_t0, Y_t0 = X[T == 0], Y[T == 0]

        if len(X_t1) == 0 or len(X_t0) == 0:
            print("    T-learner: treatment或control组数据为空")
            return results

        for model_name in self.model_types:
            try:
                # 分别训练两个模型
                train_X_t1, test_X_t1, train_Y_t1, test_Y_t1 = train_test_split(
                    X_t1, Y_t1, test_size=0.3, random_state=42
                )
                train_X_t0, test_X_t0, train_Y_t0, test_Y_t0 = train_test_split(
                    X_t0, Y_t0, test_size=0.3, random_state=42
                )

                model_t1 = self.model_manager.train_and_evaluate(
                    model_name, train_X_t1, train_Y_t1, test_X_t1, test_Y_t1
                )
                model_t0 = self.model_manager.train_and_evaluate(
                    model_name, train_X_t0, train_Y_t0, test_X_t0, test_Y_t0
                )

                # 在整个测试集上估计个体因果效应
                test_X = np.vstack([test_X_t1, test_X_t0])
                ite_t1 = model_t1['trainer'].predict_with_scaling(test_X)
                ite_t0 = model_t0['trainer'].predict_with_scaling(test_X)
                ate_estimate = np.mean(ite_t1 - ite_t0)

                results[f"{model_name}_t_learner"] = {
                    'r2_t1': model_t1['metrics_test']['r2'],
                    'r2_t0': model_t0['metrics_test']['r2'],
                    'rmse_t1': model_t1['metrics_test']['rmse'],
                    'rmse_t0': model_t0['metrics_test']['rmse'],
                    'ate_estimate': ate_estimate
                }

            except Exception as e:
                print(f"    T-learner {model_name} 失败: {str(e)}")
                continue

        return results

    def evaluate_dataset(self, config: CausalInferenceDatasetConfig) -> Dict:
        """评估单个因果推断数据集"""
        print(f"评估因果推断数据集: {config.name}")

        # 1. 数据加载
        X, T, Y = self._load_causal_inference_data(config)
        print(f"  数据形状: X={X.shape}, T={T.shape}, Y={Y.shape}")
        print(f"  Treatment分布: T=1: {np.sum(T==1)}, T=0: {np.sum(T==0)}")

        # 2. Meta-learner框架评估
        results = {}

        # S-learner评估
        print("  执行S-learner评估...")
        s_results = self._evaluate_s_learner(X, T, Y)
        results.update(s_results)

        # T-learner评估
        print("  执行T-learner评估...")
        t_results = self._evaluate_t_learner(X, T, Y)
        results.update(t_results)

        return {
            'dataset_name': config.name,
            'model_results': results,
            'data_info': {
                'n_samples': X.shape[0],
                'n_features': X.shape[1],
                'n_treated': np.sum(T == 1),
                'n_control': np.sum(T == 0),
                'treatment_column': config.treatment_column,
                'target_column': config.target_column
            }
        }

    def evaluate_datasets(self, datasets: List[CausalInferenceDatasetConfig]) -> List[Dict]:
        """评估多个因果推断数据集"""
        results = []
        for dataset_config in datasets:
            try:
                result = self.evaluate_dataset(dataset_config)
                results.append(result)
            except Exception as e:
                print(f"数据集 {dataset_config.name} 评估失败: {str(e)}")
                traceback.print_exc()
                continue

        return results


class OODEvaluator:
    """分布外泛化数据集评估器"""

    def __init__(self, model_config: ModelConfig, device: str = 'cpu'):
        self.model_manager = ModelManager(device)
        self.device = device
        self.model_types = self.model_manager.get_available_models(model_config)

    def _load_ood_data(self, config: OODDatasetConfig) -> Tuple[np.ndarray, np.ndarray, Dict[str, Tuple[np.ndarray, np.ndarray]]]:
        """加载OOD数据集"""
        # 加载训练数据（源域）
        train_data = pd.read_csv(config.train_data_path)
        train_X = train_data[config.feature_columns].values
        train_y = train_data[config.target_column].values

        # 加载测试数据（目标域）
        test_domains = {}
        for domain_name, test_path in config.test_data_paths.items():
            test_data = pd.read_csv(test_path)
            test_X = test_data[config.feature_columns].values
            test_y = test_data[config.target_column].values
            test_domains[domain_name] = (test_X, test_y)

        return train_X, train_y, test_domains

    def evaluate_dataset(self, config: OODDatasetConfig) -> Dict:
        """评估单个OOD数据集"""
        print(f"评估OOD数据集: {config.name}")

        # 1. 数据加载
        train_X, train_y, test_domains = self._load_ood_data(config)
        print(f"  训练数据形状: X={train_X.shape}, y={train_y.shape}")
        print(f"  测试域数量: {len(test_domains)}")

        results = {}

        # 2. 在每个目标域上评估
        for domain_name, (test_X, test_y) in test_domains.items():
            print(f"  评估目标域: {domain_name} (形状: {test_X.shape})")

            domain_results = {}

            # 3. 训练模型并在目标域上测试
            for model_name in self.model_types:
                try:
                    # 在源域上训练，在目标域上测试
                    model_result = self.model_manager.train_and_evaluate(
                        model_name, train_X, train_y, test_X, test_y
                    )

                    # 计算domain gap
                    domain_gap = model_result['metrics_train']['r2'] - model_result['metrics_test']['r2']

                    domain_results[model_name] = {
                        'r2_source': model_result['metrics_train']['r2'],  # 源域性能
                        'r2_target': model_result['metrics_test']['r2'],   # 目标域性能
                        'domain_gap': domain_gap,
                        'rmse_source': model_result['metrics_train']['rmse'],
                        'rmse_target': model_result['metrics_test']['rmse'],
                        'mape_target': model_result['metrics_test']['mape']
                    }

                    print(f"    {model_name}: R² Source={model_result['metrics_train']['r2']:.3f}, "
                          f"Target={model_result['metrics_test']['r2']:.3f}, Gap={domain_gap:.3f}")

                except Exception as e:
                    print(f"    模型 {model_name} 在域 {domain_name} 上失败: {str(e)}")
                    continue

            results[domain_name] = domain_results

        return {
            'dataset_name': config.name,
            'domain_results': results,
            'data_info': {
                'n_train_samples': train_X.shape[0],
                'n_features': train_X.shape[1],
                'n_test_domains': len(test_domains),
                'test_domain_names': list(test_domains.keys()),
                'target_column': config.target_column
            }
        }

    def evaluate_datasets(self, datasets: List[OODDatasetConfig]) -> List[Dict]:
        """评估多个OOD数据集"""
        results = []
        for dataset_config in datasets:
            try:
                result = self.evaluate_dataset(dataset_config)
                results.append(result)
            except Exception as e:
                print(f"数据集 {dataset_config.name} 评估失败: {str(e)}")
                traceback.print_exc()
                continue

        return results


class RealDataVisualizer:
    """真实数据集可视化器"""

    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.r2_plotter = R2Plotter()
        self.importance_plotter = ImportancePlotter()

    def generate_causal_discovery_visualizations(self, results: List[Dict]):
        """为因果发现数据集生成完整可视化"""
        print("生成因果发现数据集可视化...")

        # 1. 准备R²数据
        r2_data = []
        for result in results:
            dataset_name = result['dataset_name']
            for model_type, metrics in result['model_results'].items():
                r2_data.append({
                    'dataset': dataset_name,
                    'model_type': model_type,
                    'r2_train': metrics['r2_train'],
                    'r2_test': metrics['r2_test'],
                    'rmse_train': metrics['rmse_train'],
                    'rmse_test': metrics['rmse_test'],
                    'mape_train': metrics['mape_train'],
                    'mape_test': metrics['mape_test']
                })

        # 2. 生成R²对比箱线图
        if r2_data:
            self.r2_plotter.plot_r2_comparison(
                r2_data, self.output_dir, r2_threshold=0.5,
                annotate_all_datasets=True, annotate_effective_datasets=False
            )

        # 3. 准备特征重要性数据
        importance_data = []
        for result in results:
            dataset_name = result['dataset_name']
            node_relationships = result['node_relationships']

            for model_type, importance_dict in result['importance_results'].items():
                if 'permutation' in importance_dict:
                    for feature_name, importance_value in importance_dict['permutation'].items():
                        # 确定节点类型
                        node_type = 'others'  # 默认
                        for rel_type, nodes in node_relationships.items():
                            if feature_name in nodes:
                                node_type = rel_type
                                break

                        importance_data.append({
                            'dataset': dataset_name,
                            'model_type': model_type,
                            'feature_name': feature_name,
                            'importance': importance_value,
                            'node_type': node_type
                        })

        # 4. 生成特征重要性箱线图
        if importance_data:
            # 创建模拟的scm_objects用于兼容现有接口
            mock_scm_objects = {}
            for i, result in enumerate(results):
                mock_scm_objects[i] = {
                    'config_key': result['dataset_name'],
                    'node_relationships': result['node_relationships']
                }

            self.importance_plotter.plot_permutation_importance_comparison(
                importance_data, self.output_dir,
                scm_objects=mock_scm_objects,
                custom_functions_configs={},
                results=r2_data,
                r2_threshold=0.5,
                annotate_all_datasets=True,
                annotate_effective_datasets=False,
                effective_datasets={}
            )

        # 5. 保存详细JSON结果
        self._save_detailed_json(results, 'causal_discovery_results.json')

        # 6. 保存CSV汇总
        self._save_csv_summary(r2_data, 'causal_discovery_summary.csv')

    def generate_simple_visualizations(self, results: List[Dict], dataset_type: str):
        """为简单数据集生成基础可视化"""
        print(f"生成{dataset_type}数据集可视化...")

        # 准备数据
        plot_data = []
        if dataset_type == 'causal_inference':
            for result in results:
                dataset_name = result['dataset_name']
                for model_type, metrics in result['model_results'].items():
                    # 提取主要指标
                    if 's_learner' in model_type:
                        plot_data.append({
                            'dataset': dataset_name,
                            'model_type': model_type,
                            'r2_train': metrics.get('r2_train', 0),
                            'r2_test': metrics.get('r2_test', 0),
                            'ate_estimate': metrics.get('ate_estimate', 0)
                        })
                    elif 't_learner' in model_type:
                        plot_data.append({
                            'dataset': dataset_name,
                            'model_type': model_type,
                            'r2_t1': metrics.get('r2_t1', 0),
                            'r2_t0': metrics.get('r2_t0', 0),
                            'ate_estimate': metrics.get('ate_estimate', 0)
                        })

        elif dataset_type == 'ood':
            for result in results:
                dataset_name = result['dataset_name']
                for domain_name, domain_results in result['domain_results'].items():
                    for model_type, metrics in domain_results.items():
                        plot_data.append({
                            'dataset': f"{dataset_name}_{domain_name}",
                            'model_type': model_type,
                            'r2_source': metrics['r2_source'],
                            'r2_target': metrics['r2_target'],
                            'domain_gap': metrics['domain_gap']
                        })

        # 生成箱线图
        if plot_data:
            self._plot_simple_comparison(plot_data, dataset_type)

        # 保存CSV汇总
        self._save_csv_summary(plot_data, f'{dataset_type}_summary.csv')

    def _plot_simple_comparison(self, data: List[Dict], dataset_type: str):
        """绘制简单的模型对比图"""
        if not data:
            return

        df = pd.DataFrame(data)

        # 根据数据集类型选择主要指标
        if dataset_type == 'causal_inference':
            metric_col = 'r2_test' if 'r2_test' in df.columns else 'r2_t1'
            title = 'Causal Inference Model Performance'
            ylabel = 'R² Score'
        elif dataset_type == 'ood':
            metric_col = 'r2_target'
            title = 'Out-of-Distribution Model Performance'
            ylabel = 'Target Domain R² Score'
        else:
            return

        # 创建箱线图
        plt.figure(figsize=(12, 8))

        # 按模型类型分组
        model_types = df['model_type'].unique()
        positions = range(len(model_types))

        box_data = []
        for model_type in model_types:
            model_data = df[df['model_type'] == model_type][metric_col].values
            box_data.append(model_data)

        bp = plt.boxplot(box_data, positions=positions, patch_artist=True)

        # 设置颜色
        colors = plt.cm.Set3(np.linspace(0, 1, len(model_types)))
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)

        plt.xticks(positions, model_types, rotation=45, ha='right')
        plt.ylabel(ylabel)
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # 保存图片
        filename = f'{dataset_type}_model_comparison.png'
        plt.savefig(os.path.join(self.output_dir, filename), dpi=300, bbox_inches='tight')
        plt.close()

        print(f"  保存图片: {filename}")

    def _save_detailed_json(self, results: List[Dict], filename: str):
        """保存详细JSON结果"""
        json_path = os.path.join(self.output_dir, filename)
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        print(f"  保存JSON文件: {filename}")

    def _save_csv_summary(self, data: List[Dict], filename: str):
        """保存CSV汇总"""
        if not data:
            return

        df = pd.DataFrame(data)
        csv_path = os.path.join(self.output_dir, filename)
        df.to_csv(csv_path, index=False)
        print(f"  保存CSV文件: {filename}")


def evaluate_real_datasets(config: RealDataEvaluationConfig):
    """统一的真实数据集评估入口函数"""

    print("="*60)
    print("真实数据集评估开始")
    print("="*60)

    # 创建输出目录
    run_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(config.output_dir, run_time)
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    results = {}

    # 1. 因果发现数据集评估
    if config.causal_discovery_datasets:
        print("\n" + "="*40)
        print("评估因果发现数据集")
        print("="*40)

        cd_evaluator = CausalDiscoveryEvaluator(config.model_config, config.device)
        cd_results = cd_evaluator.evaluate_datasets(config.causal_discovery_datasets)

        if cd_results:
            # 生成可视化和保存结果
            visualizer = RealDataVisualizer(output_dir)
            visualizer.generate_causal_discovery_visualizations(cd_results)
            results['causal_discovery'] = cd_results
            print(f"因果发现数据集评估完成，共处理 {len(cd_results)} 个数据集")
        else:
            print("因果发现数据集评估失败，无有效结果")

    # 2. 因果效应评估数据集
    if config.causal_inference_datasets:
        print("\n" + "="*40)
        print("评估因果效应估计数据集")
        print("="*40)

        ci_evaluator = CausalInferenceEvaluator(config.model_config, config.device)
        ci_results = ci_evaluator.evaluate_datasets(config.causal_inference_datasets)

        if ci_results:
            # 生成可视化和保存结果
            visualizer = RealDataVisualizer(output_dir)
            visualizer.generate_simple_visualizations(ci_results, 'causal_inference')
            results['causal_inference'] = ci_results
            print(f"因果效应评估数据集评估完成，共处理 {len(ci_results)} 个数据集")
        else:
            print("因果效应评估数据集评估失败，无有效结果")

    # 3. OOD数据集评估
    if config.ood_datasets:
        print("\n" + "="*40)
        print("评估分布外泛化数据集")
        print("="*40)

        ood_evaluator = OODEvaluator(config.model_config, config.device)
        ood_results = ood_evaluator.evaluate_datasets(config.ood_datasets)

        if ood_results:
            # 生成可视化和保存结果
            visualizer = RealDataVisualizer(output_dir)
            visualizer.generate_simple_visualizations(ood_results, 'ood')
            results['ood'] = ood_results
            print(f"OOD数据集评估完成，共处理 {len(ood_results)} 个数据集")
        else:
            print("OOD数据集评估失败，无有效结果")

    print("\n" + "="*60)
    print("真实数据集评估完成")
    print(f"结果保存在: {output_dir}")
    print("="*60)

    return results


def main():
    """示例使用"""

    # 配置模型
    model_config = ModelConfig(
        ols=True,
        lasso=False,
        catboost=False,
        xgboost=True,
        lightgbm=False,
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )

    # 配置因果发现数据集
    causal_discovery_datasets = [
        CausalDiscoveryDatasetConfig(
            name="sachs",
            data_path="data/sachs/sachs_data.csv",
            adj_matrix_path="data/sachs/sachs_adj.csv",
            target_node="Akt",
            feature_columns=["PKC", "Mek", "Raf", "Jnk", "P38", "PIP2", "PIP3", "Plcg"]
        ),
        CausalDiscoveryDatasetConfig(
            name="child",
            data_path="data/child/child_data.csv",
            adj_matrix_path="data/child/child_adj.csv",
            target_node="Disease",
            feature_columns=["Age", "Sex", "LungParench", "CO2", "ChestXray"]
        )
    ]

    # 配置因果推断数据集
    causal_inference_datasets = [
        CausalInferenceDatasetConfig(
            name="ihdp",
            data_path="data/ihdp/ihdp.csv",
            target_column="y_factual",
            feature_columns=["x1", "x2", "x3", "x4", "x5"],
            treatment_column="treatment"
        )
    ]

    # 配置OOD数据集
    ood_datasets = [
        OODDatasetConfig(
            name="wilds_camelyon",
            train_data_path="data/camelyon/train.csv",
            test_data_paths={
                "hospital_1": "data/camelyon/test_hospital_1.csv",
                "hospital_2": "data/camelyon/test_hospital_2.csv"
            },
            target_column="y",
            feature_columns=["feature_1", "feature_2", "feature_3"]
        )
    ]

    # 创建评估配置
    config = RealDataEvaluationConfig(
        causal_discovery_datasets=causal_discovery_datasets,
        causal_inference_datasets=causal_inference_datasets,
        ood_datasets=ood_datasets,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        num_workers=4,
        output_dir='real_data_results'
    )

    # 执行评估
    results = evaluate_real_datasets(config)

    print("\n评估结果概览:")
    for dataset_type, type_results in results.items():
        print(f"  {dataset_type}: {len(type_results)} 个数据集")


if __name__ == "__main__":
    main()
