"""
task_config_examples.py - 任务参数配置示例

展示如何灵活配置真实数据集评估任务，支持单选、多选和全选。
"""

from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    create_sample_causal_discovery_configs,
    create_sample_causal_inference_configs,
    create_sample_ood_configs
)
from scm_test import ModelConfig
import torch


def configure_tasks_programmatically():
    """程序化配置任务参数示例"""
    
    print("程序化任务配置示例")
    print("="*50)
    
    # 配置模型
    model_config = ModelConfig(
        ols=True,
        lasso=True,
        catboost=True,
        xgboost=True,
        lightgbm=True,
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )
    
    # 示例1: 只执行因果发现评估
    print("\n示例1: 只执行因果发现评估")
    config1 = RealDataEvaluationConfig(
        causal_discovery_datasets=create_sample_causal_discovery_configs(),
        causal_inference_datasets=None,
        ood_datasets=None,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_discovery_only'
    )
    print("配置完成: 因果发现评估")
    
    # 示例2: 执行因果发现 + 因果推断评估
    print("\n示例2: 执行因果发现 + 因果推断评估")
    config2 = RealDataEvaluationConfig(
        causal_discovery_datasets=create_sample_causal_discovery_configs(),
        causal_inference_datasets=create_sample_causal_inference_configs(),
        ood_datasets=None,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_discovery_inference'
    )
    print("配置完成: 因果发现 + 因果推断评估")
    
    # 示例3: 执行因果推断 + OOD评估
    print("\n示例3: 执行因果推断 + OOD评估")
    config3 = RealDataEvaluationConfig(
        causal_discovery_datasets=None,
        causal_inference_datasets=create_sample_causal_inference_configs(),
        ood_datasets=create_sample_ood_configs(),
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_inference_ood'
    )
    print("配置完成: 因果推断 + OOD评估")
    
    # 示例4: 执行所有任务
    print("\n示例4: 执行所有任务")
    config4 = RealDataEvaluationConfig(
        causal_discovery_datasets=create_sample_causal_discovery_configs(),
        causal_inference_datasets=create_sample_causal_inference_configs(),
        ood_datasets=create_sample_ood_configs(),
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='results_complete'
    )
    print("配置完成: 所有任务")
    
    # 选择要执行的配置
    print("\n" + "="*50)
    print("选择要执行的配置:")
    print("1. 只执行因果发现评估")
    print("2. 执行因果发现 + 因果推断评估")
    print("3. 执行因果推断 + OOD评估")
    print("4. 执行所有任务")
    
    choice = input("请选择 (1-4, 默认: 1): ").strip()
    
    if choice == "2":
        selected_config = config2
        print("执行: 因果发现 + 因果推断评估")
    elif choice == "3":
        selected_config = config3
        print("执行: 因果推断 + OOD评估")
    elif choice == "4":
        selected_config = config4
        print("执行: 所有任务")
    else:
        selected_config = config1
        print("执行: 因果发现评估")
    
    # 执行评估
    print(f"\n使用设备: {selected_config.device}")
    print("开始评估...")
    
    results = evaluate_real_datasets(selected_config)
    
    # 输出结果概览
    print("\n" + "="*50)
    print("评估结果概览")
    print("="*50)
    
    total_datasets = 0
    for dataset_type, type_results in results.items():
        print(f"\n{dataset_type.upper()}:")
        print(f"  成功评估: {len(type_results)} 个数据集")
        total_datasets += len(type_results)
    
    print(f"\n总计成功评估: {total_datasets} 个数据集")
    print("评估完成！")
    
    return results


def create_custom_task_config(tasks: list, output_suffix: str = "custom"):
    """
    创建自定义任务配置的辅助函数
    
    Args:
        tasks: 任务列表，可包含 'discovery', 'inference', 'ood'
        output_suffix: 输出目录后缀
    
    Returns:
        RealDataEvaluationConfig对象
    """
    
    model_config = ModelConfig(
        ols=True,
        lasso=True,
        catboost=True,
        xgboost=True,
        lightgbm=True,
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )
    
    # 根据任务列表配置数据集
    causal_discovery_datasets = None
    causal_inference_datasets = None
    ood_datasets = None
    
    if 'discovery' in tasks:
        causal_discovery_datasets = create_sample_causal_discovery_configs()
        print(f"✓ 配置因果发现数据集: {len(causal_discovery_datasets)} 个")
    
    if 'inference' in tasks:
        causal_inference_datasets = create_sample_causal_inference_configs()
        print(f"✓ 配置因果推断数据集: {len(causal_inference_datasets)} 个")
    
    if 'ood' in tasks:
        ood_datasets = create_sample_ood_configs()
        print(f"✓ 配置OOD数据集: {len(ood_datasets)} 个")
    
    # 生成输出目录名
    output_dir = f"results_{'_'.join(sorted(tasks))}_{output_suffix}"
    
    config = RealDataEvaluationConfig(
        causal_discovery_datasets=causal_discovery_datasets,
        causal_inference_datasets=causal_inference_datasets,
        ood_datasets=ood_datasets,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir=output_dir
    )
    
    return config


def run_custom_task_examples():
    """运行自定义任务配置示例"""
    
    print("自定义任务配置示例")
    print("="*50)
    
    # 示例: 各种任务组合
    task_combinations = [
        (['discovery'], "因果发现评估"),
        (['inference'], "因果推断评估"),
        (['ood'], "OOD评估"),
        (['discovery', 'inference'], "因果发现 + 因果推断"),
        (['discovery', 'ood'], "因果发现 + OOD"),
        (['inference', 'ood'], "因果推断 + OOD"),
        (['discovery', 'inference', 'ood'], "完整评估")
    ]
    
    print("可用的任务组合:")
    for i, (tasks, description) in enumerate(task_combinations, 1):
        print(f"{i}. {description}")
    
    choice = input(f"\n请选择任务组合 (1-{len(task_combinations)}, 默认: 1): ").strip()
    
    try:
        choice_idx = int(choice) - 1 if choice else 0
        if 0 <= choice_idx < len(task_combinations):
            selected_tasks, description = task_combinations[choice_idx]
        else:
            selected_tasks, description = task_combinations[0]
    except ValueError:
        selected_tasks, description = task_combinations[0]
    
    print(f"\n选择的任务组合: {description}")
    print(f"任务列表: {selected_tasks}")
    
    # 创建配置
    config = create_custom_task_config(selected_tasks, "example")
    
    # 执行评估
    print(f"\n使用设备: {config.device}")
    print("开始评估...")
    
    results = evaluate_real_datasets(config)
    
    print(f"\n{description} 评估完成！")
    print(f"结果保存在: {config.output_dir}")
    
    return results


def main():
    """主函数 - 选择运行模式"""
    
    print("任务参数配置示例")
    print("="*60)
    print("1. 程序化配置示例")
    print("2. 自定义任务组合示例")
    print("3. 直接运行主评估程序")
    
    mode = input("\n请选择运行模式 (1-3, 默认: 3): ").strip()
    
    if mode == "1":
        return configure_tasks_programmatically()
    elif mode == "2":
        return run_custom_task_examples()
    else:
        print("启动主评估程序...")
        from real_data_evaluator import main as main_evaluator
        return main_evaluator()


if __name__ == "__main__":
    main()
