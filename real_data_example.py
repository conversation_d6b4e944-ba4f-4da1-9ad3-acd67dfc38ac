"""
real_data_example.py - 真实数据集评估使用示例

这个文件展示了如何使用real_data_evaluator.py来评估三类真实数据集：
1. 因果发现数据集（有邻接矩阵）
2. 因果推断数据集（有treatment和outcome）
3. 分布外泛化数据集（有不同域的数据）
"""

import torch
from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    CausalDiscoveryDatasetConfig,
    CausalInferenceDatasetConfig,
    OODDatasetConfig
)
from scm_test import ModelConfig


def create_sample_configs():
    """创建示例配置"""
    
    # 1. 模型配置
    model_config = ModelConfig(
        ols=True,
        lasso=True,
        catboost=False,  # 如果没有安装可以设为False
        xgboost=True,
        lightgbm=False,  # 如果没有安装可以设为False
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )
    
    # 2. 因果发现数据集配置
    causal_discovery_datasets = [
        CausalDiscoveryDatasetConfig(
            name="sachs",
            data_path="data/sachs/sachs_data.csv", 
            adj_matrix_path="data/sachs/sachs_adj.csv",
            target_node="Akt",
            feature_columns=["PKC", "Mek", "Raf", "Jnk", "P38", "PIP2", "PIP3", "Plcg"]
        ),
        CausalDiscoveryDatasetConfig(
            name="child",
            data_path="data/child/child_data.csv",
            adj_matrix_path="data/child/child_adj.csv", 
            target_node="Disease",
            feature_columns=["Age", "Sex", "LungParench", "CO2", "ChestXray"]
        ),
        CausalDiscoveryDatasetConfig(
            name="alarm",
            data_path="data/alarm/alarm_data.csv",
            adj_matrix_path="data/alarm/alarm_adj.csv", 
            target_node="HYPOVOLEMIA",
            feature_columns=["CVP", "PCWP", "HISTORY", "TPR", "BP", "CO", "HRBP", "HREK", "HRSAT", "ANAPHYLAXIS"]
        )
    ]
    
    # 3. 因果推断数据集配置
    causal_inference_datasets = [
        CausalInferenceDatasetConfig(
            name="ihdp",
            data_path="data/ihdp/ihdp.csv",
            target_column="y_factual", 
            feature_columns=["x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10"],
            treatment_column="treatment"
        ),
        CausalInferenceDatasetConfig(
            name="jobs",
            data_path="data/jobs/jobs.csv",
            target_column="re78", 
            feature_columns=["age", "education", "black", "hispanic", "married", "nodegree", "re74", "re75"],
            treatment_column="treat"
        )
    ]
    
    # 4. OOD数据集配置
    ood_datasets = [
        OODDatasetConfig(
            name="wilds_camelyon",
            train_data_path="data/camelyon/train.csv",
            test_data_paths={
                "hospital_1": "data/camelyon/test_hospital_1.csv",
                "hospital_2": "data/camelyon/test_hospital_2.csv",
                "hospital_3": "data/camelyon/test_hospital_3.csv"
            },
            target_column="y",
            feature_columns=["feature_1", "feature_2", "feature_3", "feature_4", "feature_5"]
        ),
        OODDatasetConfig(
            name="domainnet",
            train_data_path="data/domainnet/real_train.csv",
            test_data_paths={
                "clipart": "data/domainnet/clipart_test.csv",
                "painting": "data/domainnet/painting_test.csv",
                "sketch": "data/domainnet/sketch_test.csv"
            },
            target_column="label",
            feature_columns=["feat_1", "feat_2", "feat_3", "feat_4", "feat_5", "feat_6"]
        )
    ]
    
    return model_config, causal_discovery_datasets, causal_inference_datasets, ood_datasets


def run_full_evaluation():
    """运行完整的三类数据集评估"""
    
    print("创建评估配置...")
    model_config, cd_datasets, ci_datasets, ood_datasets = create_sample_configs()
    
    # 创建评估配置
    config = RealDataEvaluationConfig(
        causal_discovery_datasets=cd_datasets,
        causal_inference_datasets=ci_datasets,
        ood_datasets=ood_datasets,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        num_workers=4,
        output_dir='real_data_results'
    )
    
    print(f"使用设备: {config.device}")
    print(f"因果发现数据集: {len(cd_datasets)} 个")
    print(f"因果推断数据集: {len(ci_datasets)} 个")
    print(f"OOD数据集: {len(ood_datasets)} 个")
    
    # 执行评估
    results = evaluate_real_datasets(config)
    
    # 输出结果概览
    print("\n" + "="*50)
    print("评估结果概览")
    print("="*50)
    
    for dataset_type, type_results in results.items():
        print(f"\n{dataset_type.upper()}:")
        print(f"  成功评估数据集数量: {len(type_results)}")
        
        for result in type_results:
            dataset_name = result['dataset_name']
            if dataset_type == 'causal_discovery':
                n_models = len(result['model_results'])
                print(f"    {dataset_name}: {n_models} 个模型")
            elif dataset_type == 'causal_inference':
                n_methods = len(result['model_results'])
                print(f"    {dataset_name}: {n_methods} 个meta-learner方法")
            elif dataset_type == 'ood':
                n_domains = len(result['domain_results'])
                print(f"    {dataset_name}: {n_domains} 个目标域")
    
    return results


def run_single_type_evaluation():
    """运行单一类型数据集评估示例"""
    
    print("运行单一类型评估示例（仅因果发现数据集）...")
    
    model_config, cd_datasets, _, _ = create_sample_configs()
    
    # 只评估因果发现数据集
    config = RealDataEvaluationConfig(
        causal_discovery_datasets=cd_datasets[:1],  # 只取第一个数据集
        causal_inference_datasets=None,
        ood_datasets=None,
        model_config=model_config,
        device='cpu',  # 使用CPU以确保兼容性
        output_dir='real_data_results_single'
    )
    
    results = evaluate_real_datasets(config)
    
    print(f"单一类型评估完成，结果: {len(results)} 种类型")
    
    return results


def main():
    """主函数"""
    
    print("真实数据集评估示例")
    print("="*50)
    
    # 选择运行模式
    mode = input("选择运行模式 (1: 完整评估, 2: 单一类型评估, 默认: 1): ").strip()
    
    if mode == "2":
        results = run_single_type_evaluation()
    else:
        results = run_full_evaluation()
    
    print("\n评估完成！")
    print("结果文件已保存到相应的输出目录中。")
    
    return results


if __name__ == "__main__":
    main()
