"""
real_data_example.py - 真实数据集评估使用示例

这个文件展示了如何使用real_data_evaluator.py来评估三类真实数据集：
1. 因果发现数据集（有邻接矩阵）
2. 因果推断数据集（有treatment和outcome）
3. 分布外泛化数据集（有不同域的数据）
"""

import torch
from real_data_evaluator import (
    evaluate_real_datasets,
    RealDataEvaluationConfig,
    CausalDiscoveryDatasetConfig,
    CausalInferenceDatasetConfig,
    OODDatasetConfig
)
from scm_test import ModelConfig


def create_sample_configs():
    """创建示例配置"""
    
    # 1. 模型配置
    model_config = ModelConfig(
        ols=True,
        lasso=True,
        catboost=False,  # 如果没有安装可以设为False
        xgboost=True,
        lightgbm=False,  # 如果没有安装可以设为False
        tabpfn_default=True,
        tabpfn_mse=True,
        tabpfn_muzero=True
    )
    
    # 2. 因果发现数据集配置
    causal_discovery_datasets = [
        CausalDiscoveryDatasetConfig(
            name="sachs",
            data_path="data/sachs/sachs_data.csv", 
            adj_matrix_path="data/sachs/sachs_adj.csv",
            target_node="Akt",
            feature_columns=["PKC", "Mek", "Raf", "Jnk", "P38", "PIP2", "PIP3", "Plcg"]
        ),
        CausalDiscoveryDatasetConfig(
            name="child",
            data_path="data/child/child_data.csv",
            adj_matrix_path="data/child/child_adj.csv", 
            target_node="Disease",
            feature_columns=["Age", "Sex", "LungParench", "CO2", "ChestXray"]
        ),
        CausalDiscoveryDatasetConfig(
            name="alarm",
            data_path="data/alarm/alarm_data.csv",
            adj_matrix_path="data/alarm/alarm_adj.csv", 
            target_node="HYPOVOLEMIA",
            feature_columns=["CVP", "PCWP", "HISTORY", "TPR", "BP", "CO", "HRBP", "HREK", "HRSAT", "ANAPHYLAXIS"]
        )
    ]
    
    # 3. 因果推断数据集配置 - 多个数据集示例
    causal_inference_datasets = [
        # IHDP数据集
        CausalInferenceDatasetConfig(
            name="ihdp",
            data_path="data/ihdp/ihdp.csv",
            target_column="y_factual",
            feature_columns=["x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10"],
            treatment_column="treatment"
        ),
        # Jobs数据集
        CausalInferenceDatasetConfig(
            name="jobs",
            data_path="data/jobs/jobs.csv",
            target_column="re78",
            feature_columns=["age", "education", "black", "hispanic", "married", "nodegree", "re74", "re75"],
            treatment_column="treat"
        ),
        # Twins数据集
        CausalInferenceDatasetConfig(
            name="twins",
            data_path="data/twins/twins.csv",
            target_column="y",
            feature_columns=["gestat10", "birwt", "precare", "momage", "nprevistg", "dfageq", "feduc6", "infant", "omaps", "fmaps"],
            treatment_column="t"
        ),
        # ACIC数据集
        CausalInferenceDatasetConfig(
            name="acic_2016",
            data_path="data/acic/acic_2016.csv",
            target_column="Y",
            feature_columns=["X1", "X2", "X3", "X4", "X5", "X6", "X7", "X8", "X9", "X10", "X11", "X12"],
            treatment_column="Z"
        ),
        # News数据集
        CausalInferenceDatasetConfig(
            name="news",
            data_path="data/news/news.csv",
            target_column="outcome",
            feature_columns=["n_tokens_title", "n_tokens_content", "n_unique_tokens", "n_non_stop_words", "num_hrefs", "num_self_hrefs", "num_imgs", "num_videos"],
            treatment_column="treatment"
        )
    ]
    
    # 4. OOD数据集配置 - 多个数据集示例
    ood_datasets = [
        # WILDS Camelyon数据集
        OODDatasetConfig(
            name="wilds_camelyon",
            train_data_path="data/camelyon/train.csv",
            test_data_paths={
                "hospital_1": "data/camelyon/test_hospital_1.csv",
                "hospital_2": "data/camelyon/test_hospital_2.csv",
                "hospital_3": "data/camelyon/test_hospital_3.csv",
                "hospital_4": "data/camelyon/test_hospital_4.csv"
            },
            target_column="y",
            feature_columns=["feature_1", "feature_2", "feature_3", "feature_4", "feature_5"]
        ),
        # DomainNet数据集
        OODDatasetConfig(
            name="domainnet",
            train_data_path="data/domainnet/real_train.csv",
            test_data_paths={
                "clipart": "data/domainnet/clipart_test.csv",
                "painting": "data/domainnet/painting_test.csv",
                "sketch": "data/domainnet/sketch_test.csv",
                "infograph": "data/domainnet/infograph_test.csv",
                "quickdraw": "data/domainnet/quickdraw_test.csv"
            },
            target_column="label",
            feature_columns=["feat_1", "feat_2", "feat_3", "feat_4", "feat_5", "feat_6"]
        ),
        # PACS数据集
        OODDatasetConfig(
            name="pacs",
            train_data_path="data/pacs/photo_train.csv",
            test_data_paths={
                "art_painting": "data/pacs/art_painting_test.csv",
                "cartoon": "data/pacs/cartoon_test.csv",
                "sketch": "data/pacs/sketch_test.csv"
            },
            target_column="label",
            feature_columns=["pixel_1", "pixel_2", "pixel_3", "pixel_4", "pixel_5", "pixel_6", "pixel_7", "pixel_8"]
        ),
        # Office-Home数据集
        OODDatasetConfig(
            name="office_home",
            train_data_path="data/office_home/real_world_train.csv",
            test_data_paths={
                "art": "data/office_home/art_test.csv",
                "clipart": "data/office_home/clipart_test.csv",
                "product": "data/office_home/product_test.csv"
            },
            target_column="category",
            feature_columns=["feature_1", "feature_2", "feature_3", "feature_4", "feature_5"]
        ),
        # Terra Incognita数据集
        OODDatasetConfig(
            name="terra_incognita",
            train_data_path="data/terra/location_100_train.csv",
            test_data_paths={
                "location_38": "data/terra/location_38_test.csv",
                "location_43": "data/terra/location_43_test.csv",
                "location_46": "data/terra/location_46_test.csv"
            },
            target_column="species",
            feature_columns=["env_1", "env_2", "env_3", "env_4", "env_5", "env_6", "env_7", "env_8", "env_9", "env_10"]
        )
    ]
    
    return model_config, causal_discovery_datasets, causal_inference_datasets, ood_datasets


def run_full_evaluation():
    """运行完整的三类数据集评估"""
    
    print("创建评估配置...")
    model_config, cd_datasets, ci_datasets, ood_datasets = create_sample_configs()
    
    # 创建评估配置
    config = RealDataEvaluationConfig(
        causal_discovery_datasets=cd_datasets,
        causal_inference_datasets=ci_datasets,
        ood_datasets=ood_datasets,
        model_config=model_config,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        output_dir='real_data_results'
    )
    
    print(f"使用设备: {config.device}")
    print(f"因果发现数据集: {len(cd_datasets)} 个")
    print(f"因果推断数据集: {len(ci_datasets)} 个")
    print(f"OOD数据集: {len(ood_datasets)} 个")
    
    # 执行评估
    results = evaluate_real_datasets(config)
    
    # 输出结果概览
    print("\n" + "="*50)
    print("评估结果概览")
    print("="*50)
    
    for dataset_type, type_results in results.items():
        print(f"\n{dataset_type.upper()}:")
        print(f"  成功评估数据集数量: {len(type_results)}")
        
        for result in type_results:
            dataset_name = result['dataset_name']
            if dataset_type == 'causal_discovery':
                n_models = len(result['model_results'])
                print(f"    {dataset_name}: {n_models} 个模型")
            elif dataset_type == 'causal_inference':
                n_methods = len(result['model_results'])
                print(f"    {dataset_name}: {n_methods} 个meta-learner方法")
            elif dataset_type == 'ood':
                n_domains = len(result['domain_results'])
                print(f"    {dataset_name}: {n_domains} 个目标域")
    
    return results


def run_single_type_evaluation():
    """运行单一类型数据集评估示例"""
    
    print("运行单一类型评估示例（仅因果发现数据集）...")
    
    model_config, cd_datasets, _, _ = create_sample_configs()
    
    # 只评估因果发现数据集
    config = RealDataEvaluationConfig(
        causal_discovery_datasets=cd_datasets[:1],  # 只取第一个数据集
        causal_inference_datasets=None,
        ood_datasets=None,
        model_config=model_config,
        device='cpu',  # 使用CPU以确保兼容性
        output_dir='real_data_results_single'
    )
    
    results = evaluate_real_datasets(config)
    
    print(f"单一类型评估完成，结果: {len(results)} 种类型")
    
    return results


def main():
    """主函数"""
    
    print("真实数据集评估示例")
    print("="*50)
    
    # 选择运行模式
    mode = input("选择运行模式 (1: 完整评估, 2: 单一类型评估, 默认: 1): ").strip()
    
    if mode == "2":
        results = run_single_type_evaluation()
    else:
        results = run_full_evaluation()
    
    print("\n评估完成！")
    print("结果文件已保存到相应的输出目录中。")
    
    return results


if __name__ == "__main__":
    main()
